#!/bin/bash
set -e  # Exit on any error

args=("$@")
echo "args: ${args[@]}"
ADMIN_AUTH="${args[0]}"

if [ -z "$ADMIN_AUTH" ]; then
  echo "Error: ADMIN_AUTH is not set. Please provide the authorization token."
  echo "Usage: $0 <ADMIN_AUTH_TOKEN>"
  exit 1
fi

echo "Setting up webhook with authorization token: ${ADMIN_AUTH:0:10}..."

sudo apt-get update && sudo apt-get install -y webhook

# Create hooks.json with proper variable interpolation
touch ./hooks.json
cat > ./hooks.json << EOF
[
  {
    "id": "redeploy-webhook",
    "execute-command": "/bin/sh",
    "command-working-directory": "./",
    "incoming-payload-content-type": "application/json",
     "pass-arguments-to-command": [
      {
        "source": "string",
        "name": "-c"
      },
      {
        "source": "string",
        "name": "./redeploy.sh"
      }
    ],
    "trigger-rule": {
      "and": [
        {
          "match": {
            "parameter": {
              "source": "header",
              "name": "Authorization"
            },
            "type": "value",
            "value": "${ADMIN_AUTH}"
          }
        }
      ]
    }
  }
]
EOF

echo "hooks.json created successfully!"
echo "Contents:"
cat ./hooks.json

echo "Starting webhook server on port 2500..."
webhook -hooks ./hooks.json -verbose -port 2500
