#!/bin/bash

python3 -m grpc_tools.protoc -I. --python_out=. --pyi_out=. --grpc_python_out=. ./bridge.proto
protoc -I . --go_out=../bots/bridge --go_opt=paths=source_relative --go-grpc_out=../bots/bridge --go-grpc_opt=paths=source_relative bridge.proto
npx protoc --ts_out ../../node-manager/src/utils/bridge --proto_path . bridge.proto && npx protoc -I . --ts_out ../../node-manager/src/utils/bridge  --ts_opt server_grpc1,client_none,optimize_code_size bridge.proto