syntax = "proto3";
package bridge;

option go_package = "/bridge";

// service
service BridgeRpcService {
    rpc TickStart( TickStartRequest) returns (stream TickType);
    rpc TickStop( EmptyType) returns (GenericResponseType);
    rpc GetAccount( EmptyType) returns (GetAccountResponse);
    rpc GetPositions( EmptyType) returns (GetPositionsResponse);
    rpc GetOrders( EmptyType) returns (GetOrdersResponse);
    rpc GetAvailableSymbols( EmptyType) returns (GetAvailableSymbolsResponse);
    rpc GetTradeHistory ( GetTradeHistoryRequest) returns (GetTradeHistoryResponse);
    rpc GetTicksFrom( GetTicksFromRequest) returns (GetTicksResponse);
    rpc GetTicksRange( GetTicksRangeRequest) returns (GetTicksResponse);
    rpc CloseTrade( CloseTradeRequest) returns (GenericResponseType);
    rpc ModifyTrade( ModifyTradeRequest) returns (GenericResponseType);
    rpc PlaceTrade( PlaceTradeRequest) returns (PlaceTradeResponse);
    rpc ManageSymbol( ManageSymbolRequest) returns (ManageSymbolResponse);
    rpc GetTerminalError( EmptyType) returns (GenericResponseType);
    rpc GetSymbolTick( GetSymbolTickRequest) returns (TickType);
}

// types
message EmptyType{}

message TickType {
  float ask = 1;
  float bid = 2;
  int64 time = 3;
  optional string meta = 4;
}

message BarType {
  float open = 1;
  float high = 2;
  float low = 3;
  float close = 4;
  float volume = 6;
  float spread = 7; 
  int64 time = 5;
  optional string meta = 8;
}

message AccountPositionType{
  int64 ticket = 1;
  string symbol = 2;
  int64 type = 3;
  float volume = 4;
  float open_price = 5;
  float current_price = 6;
  float stop_loss = 7;
  float take_profit = 8;
  float profit = 9;
  string comment = 10;
  int64 magic = 11;
  optional string meta = 12;
}

message AccountOrdersType {
  int64 ticket = 1;
  string symbol = 2;
  int64 type = 3;
  float volume = 4;
  float price_open = 5;
  float price_current = 6;
  float stop_loss = 7;
  float take_profit = 8;
  string comment = 9;
  int64 time_setup = 10;
  int64 time_expiration = 11;
  int64 magic = 12;
  optional string meta = 13;
}

message SymbolsType {
  string name = 1;
  string description = 2;
  string path = 3;
  int64 digits = 4;
  float spread = 5;
  int64 time = 6;
  optional string meta = 7;
}

message TradeDealType {
  int64 ticket = 1;
  int64 order = 2;
  int64 time = 3;
  int64 type = 4;
  float entry = 5;
  string symbol = 6;
  float volume = 7;
  float price = 8;
  float commission = 9;
  float swap = 10;
  float profit = 11;
  int64 magic = 12;
  string comment = 13;
  optional string meta = 14;
}

// request messages



message GetSymbolTickRequest {
  string symbol = 1;
}
message TickStartRequest {
  string symbol = 1;
  float rate = 2;
}

message CloseTradeRequest {
  int64 ticket = 1;
  optional float volume = 2;
  optional float price = 3;
  optional float slippage = 4;
}

message GetTradeHistoryRequest {
  int64 start_date = 1;
  int64 end_date = 2;
}

message GetTicksFromRequest {
  string symbol = 1;
  int64 start_date = 2;
  int64 length = 3;
  optional string timeframe = 4; // e.g., "M1","M5","M15","M30","H1","H4","D1","W1","MN1"
}

message GetTicksRangeRequest {
  string symbol = 1;
  int64 start_date = 2;
  int64 end_date = 3;
  int64 length = 4;
  optional string timeframe = 5; // same as above
}

message ModifyTradeRequest {
  int64 ticket = 1;
  float stop_loss = 2;
  float take_profit = 3;
  optional float price = 4;
  optional int64 expiration = 5;
}

message PlaceTradeRequest {
  string symbol = 1;
  int64 action_type = 2;
  float volume = 3;
  optional float stop_loss = 4;
  optional float take_profit = 5;
  optional string comment = 6;
  optional float price =7;
  optional int64 order = 8;
  optional int64 magic = 9;
  optional float stop_limit = 10;
  optional int64 expiration = 11;
  optional int64 position = 12;
  optional int64 position_by = 13;
  optional int64 deviation = 14;
}
message ManageSymbolRequest {
  string symbol = 1;
  string action = 2;
}

// response messages

message GenericResponseType {
  int64 status = 1;
  string message = 2;
  optional string meta = 3;
}

message GetAccountResponse {
  float balance = 1;
  float equity = 2;
  float margin = 3;
  float free_margin = 4;
  float margin_level = 5;
  float profit = 6;
  string server = 7;
  int64 trade_mode = 8;
  optional string meta = 9;
  string platform = 10;
}

message GetPositionsResponse {
  repeated AccountPositionType positions = 1;
  int64 total_positions = 2;
}

message GetOrdersResponse {
  repeated AccountOrdersType orders = 1;
  int64 total_orders = 2;
}

message GetAvailableSymbolsResponse {
  repeated SymbolsType symbols = 1;
  int64 total_symbols = 2;
}

message GetTradeHistoryResponse {
  repeated TradeDealType deals = 1;
  int64 total_deals = 2;
}

message GetTicksResponse {
  repeated BarType ticks = 1;
  int64 total_ticks = 2;
}

message PlaceTradeResponse {
  int64 status = 1;
  string message = 2;
  optional string ticket = 3;
  optional string meta = 4;
}

message ManageSymbolResponse {
  int64 status = 1;
  string message = 2; 
  optional SymbolsType symbol = 3;
}

