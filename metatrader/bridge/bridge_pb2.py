# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: bridge.proto
# Protobuf Python Version: 5.29.0
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    29,
    0,
    '',
    'bridge.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0c\x62ridge.proto\x12\x06\x62ridge\"\x0b\n\tEmptyType\"N\n\x08TickType\x12\x0b\n\x03\x61sk\x18\x01 \x01(\x02\x12\x0b\n\x03\x62id\x18\x02 \x01(\x02\x12\x0c\n\x04time\x18\x03 \x01(\x03\x12\x11\n\x04meta\x18\x04 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\x8b\x01\n\x07\x42\x61rType\x12\x0c\n\x04open\x18\x01 \x01(\x02\x12\x0c\n\x04high\x18\x02 \x01(\x02\x12\x0b\n\x03low\x18\x03 \x01(\x02\x12\r\n\x05\x63lose\x18\x04 \x01(\x02\x12\x0e\n\x06volume\x18\x06 \x01(\x02\x12\x0e\n\x06spread\x18\x07 \x01(\x02\x12\x0c\n\x04time\x18\x05 \x01(\x03\x12\x11\n\x04meta\x18\x08 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\xf2\x01\n\x13\x41\x63\x63ountPositionType\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\x12\x0e\n\x06symbol\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\x03\x12\x0e\n\x06volume\x18\x04 \x01(\x02\x12\x12\n\nopen_price\x18\x05 \x01(\x02\x12\x15\n\rcurrent_price\x18\x06 \x01(\x02\x12\x11\n\tstop_loss\x18\x07 \x01(\x02\x12\x13\n\x0btake_profit\x18\x08 \x01(\x02\x12\x0e\n\x06profit\x18\t \x01(\x02\x12\x0f\n\x07\x63omment\x18\n \x01(\t\x12\r\n\x05magic\x18\x0b \x01(\x03\x12\x11\n\x04meta\x18\x0c \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\x8d\x02\n\x11\x41\x63\x63ountOrdersType\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\x12\x0e\n\x06symbol\x18\x02 \x01(\t\x12\x0c\n\x04type\x18\x03 \x01(\x03\x12\x0e\n\x06volume\x18\x04 \x01(\x02\x12\x12\n\nprice_open\x18\x05 \x01(\x02\x12\x15\n\rprice_current\x18\x06 \x01(\x02\x12\x11\n\tstop_loss\x18\x07 \x01(\x02\x12\x13\n\x0btake_profit\x18\x08 \x01(\x02\x12\x0f\n\x07\x63omment\x18\t \x01(\t\x12\x12\n\ntime_setup\x18\n \x01(\x03\x12\x17\n\x0ftime_expiration\x18\x0b \x01(\x03\x12\r\n\x05magic\x18\x0c \x01(\x03\x12\x11\n\x04meta\x18\r \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\x88\x01\n\x0bSymbolsType\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x02 \x01(\t\x12\x0c\n\x04path\x18\x03 \x01(\t\x12\x0e\n\x06\x64igits\x18\x04 \x01(\x03\x12\x0e\n\x06spread\x18\x05 \x01(\x02\x12\x0c\n\x04time\x18\x06 \x01(\x03\x12\x11\n\x04meta\x18\x07 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\xf6\x01\n\rTradeDealType\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\x12\r\n\x05order\x18\x02 \x01(\x03\x12\x0c\n\x04time\x18\x03 \x01(\x03\x12\x0c\n\x04type\x18\x04 \x01(\x03\x12\r\n\x05\x65ntry\x18\x05 \x01(\x02\x12\x0e\n\x06symbol\x18\x06 \x01(\t\x12\x0e\n\x06volume\x18\x07 \x01(\x02\x12\r\n\x05price\x18\x08 \x01(\x02\x12\x12\n\ncommission\x18\t \x01(\x02\x12\x0c\n\x04swap\x18\n \x01(\x02\x12\x0e\n\x06profit\x18\x0b \x01(\x02\x12\r\n\x05magic\x18\x0c \x01(\x03\x12\x0f\n\x07\x63omment\x18\r \x01(\t\x12\x11\n\x04meta\x18\x0e \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"&\n\x14GetSymbolTickRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\"0\n\x10TickStartRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x0c\n\x04rate\x18\x02 \x01(\x02\"\x85\x01\n\x11\x43loseTradeRequest\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\x12\x13\n\x06volume\x18\x02 \x01(\x02H\x00\x88\x01\x01\x12\x12\n\x05price\x18\x03 \x01(\x02H\x01\x88\x01\x01\x12\x15\n\x08slippage\x18\x04 \x01(\x02H\x02\x88\x01\x01\x42\t\n\x07_volumeB\x08\n\x06_priceB\x0b\n\t_slippage\">\n\x16GetTradeHistoryRequest\x12\x12\n\nstart_date\x18\x01 \x01(\x03\x12\x10\n\x08\x65nd_date\x18\x02 \x01(\x03\"o\n\x13GetTicksFromRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x12\n\nstart_date\x18\x02 \x01(\x03\x12\x0e\n\x06length\x18\x03 \x01(\x03\x12\x16\n\ttimeframe\x18\x04 \x01(\tH\x00\x88\x01\x01\x42\x0c\n\n_timeframe\"\x82\x01\n\x14GetTicksRangeRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x12\n\nstart_date\x18\x02 \x01(\x03\x12\x10\n\x08\x65nd_date\x18\x03 \x01(\x03\x12\x0e\n\x06length\x18\x04 \x01(\x03\x12\x16\n\ttimeframe\x18\x05 \x01(\tH\x00\x88\x01\x01\x42\x0c\n\n_timeframe\"\x92\x01\n\x12ModifyTradeRequest\x12\x0e\n\x06ticket\x18\x01 \x01(\x03\x12\x11\n\tstop_loss\x18\x02 \x01(\x02\x12\x13\n\x0btake_profit\x18\x03 \x01(\x02\x12\x12\n\x05price\x18\x04 \x01(\x02H\x00\x88\x01\x01\x12\x17\n\nexpiration\x18\x05 \x01(\x03H\x01\x88\x01\x01\x42\x08\n\x06_priceB\r\n\x0b_expiration\"\xd8\x03\n\x11PlaceTradeRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x13\n\x0b\x61\x63tion_type\x18\x02 \x01(\x03\x12\x0e\n\x06volume\x18\x03 \x01(\x02\x12\x16\n\tstop_loss\x18\x04 \x01(\x02H\x00\x88\x01\x01\x12\x18\n\x0btake_profit\x18\x05 \x01(\x02H\x01\x88\x01\x01\x12\x14\n\x07\x63omment\x18\x06 \x01(\tH\x02\x88\x01\x01\x12\x12\n\x05price\x18\x07 \x01(\x02H\x03\x88\x01\x01\x12\x12\n\x05order\x18\x08 \x01(\x03H\x04\x88\x01\x01\x12\x12\n\x05magic\x18\t \x01(\x03H\x05\x88\x01\x01\x12\x17\n\nstop_limit\x18\n \x01(\x02H\x06\x88\x01\x01\x12\x17\n\nexpiration\x18\x0b \x01(\x03H\x07\x88\x01\x01\x12\x15\n\x08position\x18\x0c \x01(\x03H\x08\x88\x01\x01\x12\x18\n\x0bposition_by\x18\r \x01(\x03H\t\x88\x01\x01\x12\x16\n\tdeviation\x18\x0e \x01(\x03H\n\x88\x01\x01\x42\x0c\n\n_stop_lossB\x0e\n\x0c_take_profitB\n\n\x08_commentB\x08\n\x06_priceB\x08\n\x06_orderB\x08\n\x06_magicB\r\n\x0b_stop_limitB\r\n\x0b_expirationB\x0b\n\t_positionB\x0e\n\x0c_position_byB\x0c\n\n_deviation\"5\n\x13ManageSymbolRequest\x12\x0e\n\x06symbol\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x02 \x01(\t\"R\n\x13GenericResponseType\x12\x0e\n\x06status\x18\x01 \x01(\x03\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x11\n\x04meta\x18\x03 \x01(\tH\x00\x88\x01\x01\x42\x07\n\x05_meta\"\xd2\x01\n\x12GetAccountResponse\x12\x0f\n\x07\x62\x61lance\x18\x01 \x01(\x02\x12\x0e\n\x06\x65quity\x18\x02 \x01(\x02\x12\x0e\n\x06margin\x18\x03 \x01(\x02\x12\x13\n\x0b\x66ree_margin\x18\x04 \x01(\x02\x12\x14\n\x0cmargin_level\x18\x05 \x01(\x02\x12\x0e\n\x06profit\x18\x06 \x01(\x02\x12\x0e\n\x06server\x18\x07 \x01(\t\x12\x12\n\ntrade_mode\x18\x08 \x01(\x03\x12\x11\n\x04meta\x18\t \x01(\tH\x00\x88\x01\x01\x12\x10\n\x08platform\x18\n \x01(\tB\x07\n\x05_meta\"_\n\x14GetPositionsResponse\x12.\n\tpositions\x18\x01 \x03(\x0b\x32\x1b.bridge.AccountPositionType\x12\x17\n\x0ftotal_positions\x18\x02 \x01(\x03\"T\n\x11GetOrdersResponse\x12)\n\x06orders\x18\x01 \x03(\x0b\x32\x19.bridge.AccountOrdersType\x12\x14\n\x0ctotal_orders\x18\x02 \x01(\x03\"Z\n\x1bGetAvailableSymbolsResponse\x12$\n\x07symbols\x18\x01 \x03(\x0b\x32\x13.bridge.SymbolsType\x12\x15\n\rtotal_symbols\x18\x02 \x01(\x03\"T\n\x17GetTradeHistoryResponse\x12$\n\x05\x64\x65\x61ls\x18\x01 \x03(\x0b\x32\x15.bridge.TradeDealType\x12\x13\n\x0btotal_deals\x18\x02 \x01(\x03\"G\n\x10GetTicksResponse\x12\x1e\n\x05ticks\x18\x01 \x03(\x0b\x32\x0f.bridge.BarType\x12\x13\n\x0btotal_ticks\x18\x02 \x01(\x03\"q\n\x12PlaceTradeResponse\x12\x0e\n\x06status\x18\x01 \x01(\x03\x12\x0f\n\x07message\x18\x02 \x01(\t\x12\x13\n\x06ticket\x18\x03 \x01(\tH\x00\x88\x01\x01\x12\x11\n\x04meta\x18\x04 \x01(\tH\x01\x88\x01\x01\x42\t\n\x07_ticketB\x07\n\x05_meta\"l\n\x14ManageSymbolResponse\x12\x0e\n\x06status\x18\x01 \x01(\x03\x12\x0f\n\x07message\x18\x02 \x01(\t\x12(\n\x06symbol\x18\x03 \x01(\x0b\x32\x13.bridge.SymbolsTypeH\x00\x88\x01\x01\x42\t\n\x07_symbol2\x98\x08\n\x10\x42ridgeRpcService\x12\x39\n\tTickStart\x12\x18.bridge.TickStartRequest\x1a\x10.bridge.TickType0\x01\x12:\n\x08TickStop\x12\x11.bridge.EmptyType\x1a\x1b.bridge.GenericResponseType\x12;\n\nGetAccount\x12\x11.bridge.EmptyType\x1a\x1a.bridge.GetAccountResponse\x12?\n\x0cGetPositions\x12\x11.bridge.EmptyType\x1a\x1c.bridge.GetPositionsResponse\x12\x39\n\tGetOrders\x12\x11.bridge.EmptyType\x1a\x19.bridge.GetOrdersResponse\x12M\n\x13GetAvailableSymbols\x12\x11.bridge.EmptyType\x1a#.bridge.GetAvailableSymbolsResponse\x12R\n\x0fGetTradeHistory\x12\x1e.bridge.GetTradeHistoryRequest\x1a\x1f.bridge.GetTradeHistoryResponse\x12\x45\n\x0cGetTicksFrom\x12\x1b.bridge.GetTicksFromRequest\x1a\x18.bridge.GetTicksResponse\x12G\n\rGetTicksRange\x12\x1c.bridge.GetTicksRangeRequest\x1a\x18.bridge.GetTicksResponse\x12\x44\n\nCloseTrade\x12\x19.bridge.CloseTradeRequest\x1a\x1b.bridge.GenericResponseType\x12\x46\n\x0bModifyTrade\x12\x1a.bridge.ModifyTradeRequest\x1a\x1b.bridge.GenericResponseType\x12\x43\n\nPlaceTrade\x12\x19.bridge.PlaceTradeRequest\x1a\x1a.bridge.PlaceTradeResponse\x12I\n\x0cManageSymbol\x12\x1b.bridge.ManageSymbolRequest\x1a\x1c.bridge.ManageSymbolResponse\x12\x42\n\x10GetTerminalError\x12\x11.bridge.EmptyType\x1a\x1b.bridge.GenericResponseType\x12?\n\rGetSymbolTick\x12\x1c.bridge.GetSymbolTickRequest\x1a\x10.bridge.TickTypeB\tZ\x07/bridgeb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'bridge_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z\007/bridge'
  _globals['_EMPTYTYPE']._serialized_start=24
  _globals['_EMPTYTYPE']._serialized_end=35
  _globals['_TICKTYPE']._serialized_start=37
  _globals['_TICKTYPE']._serialized_end=115
  _globals['_BARTYPE']._serialized_start=118
  _globals['_BARTYPE']._serialized_end=257
  _globals['_ACCOUNTPOSITIONTYPE']._serialized_start=260
  _globals['_ACCOUNTPOSITIONTYPE']._serialized_end=502
  _globals['_ACCOUNTORDERSTYPE']._serialized_start=505
  _globals['_ACCOUNTORDERSTYPE']._serialized_end=774
  _globals['_SYMBOLSTYPE']._serialized_start=777
  _globals['_SYMBOLSTYPE']._serialized_end=913
  _globals['_TRADEDEALTYPE']._serialized_start=916
  _globals['_TRADEDEALTYPE']._serialized_end=1162
  _globals['_GETSYMBOLTICKREQUEST']._serialized_start=1164
  _globals['_GETSYMBOLTICKREQUEST']._serialized_end=1202
  _globals['_TICKSTARTREQUEST']._serialized_start=1204
  _globals['_TICKSTARTREQUEST']._serialized_end=1252
  _globals['_CLOSETRADEREQUEST']._serialized_start=1255
  _globals['_CLOSETRADEREQUEST']._serialized_end=1388
  _globals['_GETTRADEHISTORYREQUEST']._serialized_start=1390
  _globals['_GETTRADEHISTORYREQUEST']._serialized_end=1452
  _globals['_GETTICKSFROMREQUEST']._serialized_start=1454
  _globals['_GETTICKSFROMREQUEST']._serialized_end=1565
  _globals['_GETTICKSRANGEREQUEST']._serialized_start=1568
  _globals['_GETTICKSRANGEREQUEST']._serialized_end=1698
  _globals['_MODIFYTRADEREQUEST']._serialized_start=1701
  _globals['_MODIFYTRADEREQUEST']._serialized_end=1847
  _globals['_PLACETRADEREQUEST']._serialized_start=1850
  _globals['_PLACETRADEREQUEST']._serialized_end=2322
  _globals['_MANAGESYMBOLREQUEST']._serialized_start=2324
  _globals['_MANAGESYMBOLREQUEST']._serialized_end=2377
  _globals['_GENERICRESPONSETYPE']._serialized_start=2379
  _globals['_GENERICRESPONSETYPE']._serialized_end=2461
  _globals['_GETACCOUNTRESPONSE']._serialized_start=2464
  _globals['_GETACCOUNTRESPONSE']._serialized_end=2674
  _globals['_GETPOSITIONSRESPONSE']._serialized_start=2676
  _globals['_GETPOSITIONSRESPONSE']._serialized_end=2771
  _globals['_GETORDERSRESPONSE']._serialized_start=2773
  _globals['_GETORDERSRESPONSE']._serialized_end=2857
  _globals['_GETAVAILABLESYMBOLSRESPONSE']._serialized_start=2859
  _globals['_GETAVAILABLESYMBOLSRESPONSE']._serialized_end=2949
  _globals['_GETTRADEHISTORYRESPONSE']._serialized_start=2951
  _globals['_GETTRADEHISTORYRESPONSE']._serialized_end=3035
  _globals['_GETTICKSRESPONSE']._serialized_start=3037
  _globals['_GETTICKSRESPONSE']._serialized_end=3108
  _globals['_PLACETRADERESPONSE']._serialized_start=3110
  _globals['_PLACETRADERESPONSE']._serialized_end=3223
  _globals['_MANAGESYMBOLRESPONSE']._serialized_start=3225
  _globals['_MANAGESYMBOLRESPONSE']._serialized_end=3333
  _globals['_BRIDGERPCSERVICE']._serialized_start=3336
  _globals['_BRIDGERPCSERVICE']._serialized_end=4384
# @@protoc_insertion_point(module_scope)
