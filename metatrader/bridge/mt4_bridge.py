import asyncio
import websockets
import json
import threading
import uuid
from typing import Optional, Any, Dict
import datetime
from datetime import timezone
import time


class MtApiError(Exception):
    """
    Custom exception to represent errors returned from the MetaTrader EA.
    """

    pass


class Metatrader4:
    """
    A Python client that connects to the MetaTrader Expert Advisor via WebSocket
    and sends commands (MessageType=0). The EA responds with MessageType=1 (Response).

    Each command is assigned a unique command_id. The EA will echo that command_id
    back in the Response message payload, allowing us to match requests & responses.
    """

    MESSAGE_TYPE_COMMAND = 0  # from the C# and MQL4 code: enum MessageType.Command
    MESSAGE_TYPE_RESPONSE = 1  # Response
    MESSAGE_TYPE_EVENT = 2  # Event (we can handle if needed)
    MESSAGE_TYPE_EXPERT_LIST = 3
    MESSAGE_TYPE_EXPERT_ADDED = 4
    MESSAGE_TYPE_EXPERT_REMOVED = 5
    MESSAGE_TYPE_NOTIFICATION = 6

    NOTIFICATION_TYPE_CLIENT_READY = 0

    EVENT_TYPE_TICK = 4
    # etc. for ExpertList=3, ExpertAdded=4, ExpertRemoved=5, Notification=6 ...

    def __init__(
        self,
        host: str = "127.0.0.1",
        port: int = 8222,
        loop: Optional[asyncio.AbstractEventLoop] = None,
        max_retries: int = 5,
        retry_delay: float = 5.0,
    ):
        """
        :param host: The host/IP of the machine running the EA's WebSocket server
        :param port: The port used by the EA's WebSocket server
        :param expert_handle: The integer handle that identifies this Expert (like WindowHandle)
        :param loop: An existing event loop (optional)
        :param max_retries: Maximum number of reconnection attempts
        :param retry_delay: Delay between reconnection attempts in seconds
        """
        self.host = host
        self.port = port
        self.url = f"ws://{host}:{port}/ws"
        self.expert_handle = None
        self.max_retries = max_retries
        self.retry_delay = retry_delay
        self.is_connected = False
        self.consecutive_failures = 0
        self._loop = loop or asyncio.get_event_loop()
        self._ws: websockets.ClientConnection = None
        self._next_command_id = 1
        self._pending: Dict[int, asyncio.Future] = {}
        self._notifications: Dict[int, asyncio.Future] = {}
        self._receive_task: Optional[asyncio.Task] = None
        self._cmd_id_lock = asyncio.Lock()
        self.current_tick = None

    async def connect(self, retry_count: int = 0):
        """
        Connects to the EA's WebSocket and starts the background receiver.
        Implements retry logic for connection failures.

        :param retry_count: Current retry attempt (used internally)
        :return: Self instance for method chaining
        :raises: Exception if max retries exceeded
        """
        try:
            self._ws = await websockets.connect(self.url)
            print(f"Connected to {self.url}")

            # Start the background task to receive messages
            self._receive_task = asyncio.create_task(self._receive_loop())

            await self.init_expert_handle(20)
            self.is_connected = True
            return self

        except (
            websockets.exceptions.WebSocketException,
            asyncio.TimeoutError,
            MtApiError,
        ) as e:
            if retry_count >= self.max_retries:
                print(f"Failed to connect after {self.max_retries} attempts: {str(e)}")
                raise Exception(f"Max connection retries exceeded: {str(e)}")

            retry_count += 1
            wait_time = self.retry_delay * retry_count
            print(
                f"Connection attempt {retry_count} failed: {str(e)}. Retrying in {wait_time} seconds..."
            )

            await asyncio.sleep(wait_time)
            return await self.connect(retry_count)

    async def reconnect(self):
        """
        Attempts to reconnect to the EA's WebSocket after a disconnection.
        """
        if self._ws:
            await self._ws.close()
        if self._receive_task and not self._receive_task.done():
            self._receive_task.cancel()

        # Reset connection state
        self._pending.clear()
        self._notifications.clear()
        self.is_connected = False

        # Attempt to reconnect
        return await self.connect()

    async def init_expert_handle(self, timeout: float = 5.0) -> int:
        self.expert_handle = await self._send_notification(
            self.NOTIFICATION_TYPE_CLIENT_READY, timeout=timeout
        )
        print(f"Connected to {self.url} with expert_handle={self.expert_handle}")

    async def disconnect(self):
        """
        Gracefully closes the connection.
        """
        if self._ws:
            await self._ws.close()
        if self._receive_task:
            self._receive_task.cancel()

    async def _send_notification(self, notification_type: int, timeout: float = 10.0):
        fut = self._loop.create_future()
        self._notifications[notification_type] = fut

        msg = f"{self.MESSAGE_TYPE_NOTIFICATION};{notification_type}"

        print(f"Sending notification: {msg}")

        # Send it
        await self._ws.send(msg)

        try:
            response_data = await asyncio.wait_for(fut, timeout=timeout)
            # print(f"Received response: {response_data}")
        finally:
            # Cleanup the pending dict
            if notification_type in self._notifications:
                del self._notifications[notification_type]

        try:
            expert_handle = int(response_data)
        except ValueError:
            raise MtApiError("Malformed response from EA (not an int).")

        return expert_handle

    async def ensure_connected(self):
        """
        Ensures the connection is active before sending commands.
        Attempts to reconnect if disconnected.

        :return: True if connected, False if reconnection failed
        """

        try:
            await self._ws.ping()
        except websockets.exceptions.ConnectionClosed as e:
            print("Connection lost, attempting to reconnect...")
            try:
                await self.reconnect()
                return True
            except Exception as e:
                print(f"Reconnection failed: {str(e)}")
                return False
        return True

    async def _send_command(
        self,
        command_type: int,
        payload: dict,
        timeout: float = 10.0,
        should_wait: bool = True,
    ) -> Any:
        """
        Sends a command message with connection check and retry logic.
        """
        # Check connection status and try to reconnect if needed
        if not await self.ensure_connected():
            raise MtApiError("Not connected to MT4 server")

        # get the next command_id in a threadsafe manner
        async with self._cmd_id_lock:
            command_id = self._next_command_id
            self._next_command_id += 1

        # Create a future to track the response for command_id
        fut = self._loop.create_future()
        self._pending[command_id] = fut

        # We must send a string with format:
        # "<MessageType>;<rest-of-payload>"
        # For commands, rest-of-payload is: "expert_handle;command_id;command_type;JSON-payload"
        # so: "0;expert_handle;command_id;command_type;{...}"

        # Convert payload to JSON
        payload_str = json.dumps(payload)
        msg = f"{self.MESSAGE_TYPE_COMMAND};{self.expert_handle};{command_id};{command_type};{payload_str}"

        # print(f"Sending command: {msg}")

        # Send it
        await self._ws.send(msg)

        if not should_wait:
            return

        # Await the future or timeout
        try:
            response_data = await asyncio.wait_for(fut, timeout=timeout)
        finally:
            # Cleanup the pending dict
            if command_id in self._pending:
                del self._pending[command_id]

        # Check if the EA reported an error
        # MQL code returns something like:
        # { "ErrorCode": "0", "Value": ... }   on success
        # or { "ErrorCode": 123, "ErrorMessage": "..." } on error
        if not isinstance(response_data, dict) and isinstance(response_data, list):
            raise MtApiError("Malformed response from EA (not a dict).")

        error_code = response_data.get("ErrorCode", 0)
        if isinstance(error_code, str) and error_code.isdigit():
            error_code = int(error_code)
        if error_code != 0:
            # EA returned an error
            msg = response_data.get("ErrorMessage", "Unknown error from EA")
            raise MtApiError(f"EA Error {error_code}: {msg}")

        # Return the 'Value' (if present) or entire object
        return response_data.get("Value", None)

    async def _receive_loop(self):
        """
        Background task that receives messages from the EA and dispatches them
        to the correct futures (for responses) or signals events.
        Now includes disconnect detection and reconnection logic.
        """
        try:
            async for raw_msg in self._ws:
                # raw_msg is like "1;{...}" for a Response,
                # or "2;{...}" for an Event, etc.
                # print(f"Received: {raw_msg}")
                # Split out the messageType
                parts = raw_msg.split(";", 1)
                if len(parts) != 2:
                    # Malformed
                    continue
                msg_type_str, payload_str = parts
                try:
                    msg_type = int(msg_type_str)
                except ValueError:
                    continue

                if msg_type == self.MESSAGE_TYPE_RESPONSE:
                    # The payload is presumably "expertHandle;commandId;JSON"
                    # But from the C# code, we see that for a Response,
                    # the EA returns: "1;expertHandle;commandId;someJson"
                    # Actually your MQL code sets the final string to:
                    #   "1;<payload>"
                    # and the <payload> is "expertHandle;commandId;{json}"
                    # Then the C# parser splits by ";", grabs [0], [1], [2] -> expert, cmdId, json.
                    # So let's replicate that:

                    sub_parts = payload_str.split(";", 2)
                    if len(sub_parts) != 3:
                        # Something off
                        continue

                    try:
                        # sub_parts[0] = expertHandle
                        # sub_parts[1] = commandId
                        # sub_parts[2] = json
                        command_id = int(sub_parts[1])
                    except ValueError:
                        continue

                    # the sub_parts[2] is the JSON
                    json_str = sub_parts[2]

                    try:
                        data = json.loads(json_str)
                    except json.JSONDecodeError:
                        print("omo")
                        data = None

                    # If we have a pending future, set the result
                    fut = self._pending.get(command_id, None)
                    if fut and not fut.done():
                        fut.set_result(data)

                elif msg_type == self.MESSAGE_TYPE_EVENT:
                    sub_parts = payload_str.split(";", 10)

                    if len(sub_parts) <= 2:
                        # Something off
                        continue
                    try:
                        # sub_parts[0] = expertHandle
                        expert_handle = int(sub_parts[0])
                    except ValueError:
                        continue

                    event_type = int(sub_parts[1])

                    if event_type == self.EVENT_TYPE_TICK:
                        json_str = sub_parts[2]

                        try:
                            data = json.loads(json_str)
                            self.current_tick = data["Tick"]

                        except json.JSONDecodeError:
                            print("omo")
                            data = None

                    pass

                elif msg_type == self.MESSAGE_TYPE_EXPERT_LIST:

                    sub_parts = payload_str.split(";", 10)
                    if len(sub_parts) != 1:
                        # Something off
                        continue

                    try:
                        # sub_parts[0] = expertHandle
                        expert_handle = int(sub_parts[0])
                    except ValueError:
                        continue

                    # If we have a pending future, set the result
                    fut = self._notifications.get(
                        self.NOTIFICATION_TYPE_CLIENT_READY, None
                    )
                    if fut and not fut.done():
                        fut.set_result(expert_handle)

                else:
                    # You can also handle ExpertList=3, ExpertAdded=4, ExpertRemoved=5, Notification=6 ...
                    pass

        except websockets.exceptions.ConnectionClosed as e:
            print(f"WebSocket connection closed unexpectedly: {e}")
            self.is_connected = False
            # Set exceptions for all pending futures
            for cmd_id, fut in self._pending.items():
                if not fut.done():
                    fut.set_exception(MtApiError(f"Connection closed: {e}"))

        except asyncio.CancelledError:
            # The task is being cancelled (e.g. on disconnect)
            print("Receive loop cancelled")
            pass

        except Exception as ex:
            print(f"Unexpected error in receive loop: {str(ex)}")
            # An unexpected error in receiving
            for cmd_id, fut in self._pending.items():
                if not fut.done():
                    fut.set_exception(ex)
            self.is_connected = False

    def get_latest_tick(self):
        return self.current_tick

    async def get_last_error(self, timeout: float = 10.0):
        command_type = 26
        result = await self._send_command(command_type, {}, timeout=timeout)
        payload = {"ErrorCode": result}
        description = await self._send_command(39, payload, timeout=timeout)
        return f"{payload['ErrorCode']}: {description}"

    async def get_symbol_tick(self, symbol: str, timeout: float = 10.0):
        command_type = 288
        payload = {"Symbol": symbol}
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_account_info(self, timeout: float = 10.0):
        command_type = 291
        payload = {}
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_pending_orders(self, timeout: float = 10.0):
        command_type = 293
        payload = {}
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_available_symbols(self, selected=False, timeout: float = 10.0):
        command_type = 295
        payload = {"Selected": selected}
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_open_positions(self, timeout: float = 10.0):
        command_type = 292
        payload = {}
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_trade_history(
        self, start_date: int = None, end_date: int = None, timeout: float = 10.0
    ):

        command_type = 294
        payload = {}
        result = await self._send_command(command_type, payload, timeout=timeout)
        isArray = isinstance(result, list)
        if isArray:
            if start_date is None:
                start_date = int(
                    datetime.datetime(1970, 1, 1, tzinfo=timezone.utc).timestamp()
                )
            if end_date is None:
                end_date = int(datetime.datetime.now(tz=timezone.utc).timestamp())
            # filter based on start and end date
            result = [
                x
                for x in result
                if x["OpenTime"] >= start_date and x["OpenTime"] <= end_date
            ]
        return result

    async def get_ticks_from(
        self,
        symbol="EURUSD",
        start_date=None,
        length=1000,
        timeframe: str | None = None,
        timeout: float = 10.0,
    ):
        if start_date is None:
            start_date = int(
                datetime.datetime(2025, 1, 20, tzinfo=timezone.utc).timestamp()
            )

        command_type = 284
        tf = (timeframe or "M1").upper()
        tf_map = {
            "M1": 1,
            "M2": 2,
            "M3": 3,
            "M4": 4,
            "M5": 5,
            "M10": 10,
            "M15": 15,
            "M30": 30,
            "H1": 60,
            "H4": 240,
            "H12": 720,
            "D1": 1440,
            "W1": 10080,
            "MN1": 43200,
        }
        mt_tf = tf_map.get(tf, 1)
        payload = {
            "SymbolName": symbol,
            "Timeframe": mt_tf,
            "CopyRatesType": 2,
            "StartTime": start_date,
            "Count": length,
        }
        print(payload)
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_ticks_range(
        self,
        symbol="EURUSD",
        start_date=None,
        end_date=None,
        timeframe: str | None = None,
        timeout: float = 10.0,
    ):
        if start_date is None:
            start_date = int(
                datetime.datetime(1970, 1, 1, tzinfo=timezone.utc).timestamp()
            )
        if end_date is None:
            end_date = int(datetime.datetime.now(tz=timezone.utc).timestamp())

        command_type = 284
        tf = (timeframe or "M1").upper()
        tf_map = {
            "M1": 1,
            "M2": 2,
            "M3": 3,
            "M4": 4,
            "M5": 5,
            "M10": 6,
            "M15": 7,
            "M30": 8,
            "H1": 9,
            "H4": 10,
            "H12": 11,
            "D1": 12,
            "W1": 13,
            "MN1": 14,
        }
        mt_tf = tf_map.get(tf, 1)
        payload = {
            "SymbolName": symbol,
            "Timeframe": mt_tf,
            "CopyRatesType": 3,
            "StartTime": start_date,
            "StopTime": end_date,
        }
        print(payload)
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def get_symbol(self, symbol="EURUSD", timeout: float = 10.0):
        result = await self._send_command(296, {"Symbol": symbol}, timeout=timeout)

        return result

    async def manage_symbol(
        self, symbol="EURUSD", action="select", period=60, timeout: float = 10.0
    ):
        # select in market watch
        result = await self._send_command(
            202, {"Name": symbol, "Select": action == "select"}, timeout=timeout
        )

        if action == "select":
            command_type = 262
            payload = {
                "ChartId": 0,
                "Symbol": symbol,
                "Period": period,
            }
            result = await self._send_command(
                command_type, payload, timeout=None, should_wait=False
            )

            result = await self.get_symbol(symbol=symbol)
            return result

    async def place_trade(
        self,
        action,
        symbol,
        volume,
        slippage=None,
        price=None,
        magic=6161,
        expiration=None,
        stop_loss=None,
        take_profit=None,
        comment="default comment",
        timeout: float = 10.0,
    ):
        command_type = 1
        payload = {
            "Symbol": symbol,
            "Cmd": action,
            "Volume": volume,
            "Comment": comment,
        }
        if price is not None:
            payload["Price"] = price

        if slippage is not None:
            payload["Slippage"] = slippage

        if magic is not None:
            payload["Magic"] = magic

        if expiration is not None:
            payload["Expiration"] = expiration

        if stop_loss is not None:
            payload["StopLoss"] = stop_loss

        if take_profit is not None:
            payload["TakeProfit"] = take_profit

        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def modify_trade(
        self,
        ticket,
        price,
        expiration,
        stop_loss,
        take_profit,
        timeout: float = 10.0,
    ):
        command_type = 12
        payload = {
            "Ticket": ticket,
            "Price": price,
            "Stoploss": stop_loss,
            "Takeprofit": take_profit,
            "Expiration": expiration,
        }
        result = await self._send_command(command_type, payload, timeout=timeout)
        return result

    async def close_trade(
        self, ticket, lots=None, price=None, slippage=None, timeout: float = 10.0
    ):
        command_type = 2
        payload = {
            "Ticket": ticket,
        }

        if lots is not None:
            payload["Lots"] = lots

        if price is not None:
            payload["Price"] = price

        if slippage is not None:
            payload["Slippage"] = slippage

        result = await self._send_command(command_type, payload, timeout=timeout)
        return result


async def main():
    client = await Metatrader4(host="127.0.0.1", port=5010).connect()

    # test
    try:
        # # Example usage

        # balance = await client.get_account_info()
        # print("Account Balance:", balance)

        # pending_orders = await client.get_pending_orders()
        # print("Pending Orders:", pending_orders)

        # available_symbols = await client.get_available_symbols()
        # print("Available Symbols:", available_symbols)

        # open_positions = await client.get_open_positions()
        # print("Open Positions:", open_positions)

        # response = await client.get_trade_history()
        # print("Response:", response)

        # response = await client.get_ticks_range()
        # print("Response:", response)

        # response = await client.get_ticks_from(length=10, start_date=**********)
        # print("Response:", response)

        response = await client.manage_symbol(
            symbol="AUDUSD",
        )
        print("Response:", response)

        # response = await client.place_trade(symbol="EURUSD",action=0, volume=0.01)
        # print("Response:", response)

        # response = await client.modify_trade(
        #     ticket=984996893,
        #     price=0,
        #     stop_loss=1.0415,
        #     take_profit=1.0615,
        #     expiration=0,
        # )
        # print("Response:", response)

        # response = await client.close_trade(ticket=984997796)
        # print("Response:", response)

        # response = client.get_latest_tick()
        # print("Response:", response)

        # error = await client.get_last_error()
        # print("Error:", error)

        # Disconnect

    finally:
        await client.disconnect()


if __name__ == "__main__":
    asyncio.run(main())
