"""
Timestamp conversion utilities for MT4/MT5 bridge service.

This module provides utilities to handle timestamp conversion between:
- Milliseconds (from client requests) to seconds (for MT4/MT5)
- Timezone-aware datetime objects for consistent UTC handling
- Proper timezone conversion for remote servers
"""

from datetime import datetime, timezone
from typing import Union, Optional
import time


class TimestampConverter:
    """
    Utility class for handling timestamp conversions and timezone operations
    for MT4/MT5 bridge service.
    """
    
    @staticmethod
    def milliseconds_to_seconds(timestamp_ms: Union[int, float, str]) -> int:
        """
        Convert milliseconds timestamp to seconds timestamp.
        
        Args:
            timestamp_ms: Timestamp in milliseconds (epoch time)
            
        Returns:
            int: Timestamp in seconds (Unix timestamp)
            
        Raises:
            ValueError: If timestamp cannot be converted
        """
        try:
            timestamp_ms = float(timestamp_ms)
            # Convert milliseconds to seconds
            timestamp_seconds = int(timestamp_ms / 1000)
            return timestamp_seconds
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid timestamp format: {timestamp_ms}") from e
    
    @staticmethod
    def seconds_to_milliseconds(timestamp_s: Union[int, float]) -> int:
        """
        Convert seconds timestamp to milliseconds timestamp.
        
        Args:
            timestamp_s: Timestamp in seconds (Unix timestamp)
            
        Returns:
            int: Timestamp in milliseconds (epoch time)
        """
        try:
            return int(float(timestamp_s) * 1000)
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid timestamp format: {timestamp_s}") from e
    
    @staticmethod
    def timestamp_to_utc_datetime(timestamp: Union[int, float, str], 
                                  is_milliseconds: bool = True) -> datetime:
        """
        Convert timestamp to UTC datetime object.
        
        Args:
            timestamp: Timestamp value
            is_milliseconds: Whether timestamp is in milliseconds (default: True)
            
        Returns:
            datetime: UTC datetime object
            
        Raises:
            ValueError: If timestamp cannot be converted
        """
        try:
            timestamp_float = float(timestamp)
            
            if is_milliseconds:
                timestamp_seconds = timestamp_float / 1000
            else:
                timestamp_seconds = timestamp_float
            
            # Create UTC datetime object
            dt = datetime.fromtimestamp(timestamp_seconds, tz=timezone.utc)
            return dt
            
        except (ValueError, TypeError, OSError) as e:
            raise ValueError(f"Invalid timestamp: {timestamp}") from e
    
    @staticmethod
    def datetime_to_timestamp(dt: datetime, as_milliseconds: bool = True) -> int:
        """
        Convert datetime object to timestamp.
        
        Args:
            dt: Datetime object
            as_milliseconds: Whether to return timestamp in milliseconds (default: True)
            
        Returns:
            int: Timestamp value
        """
        try:
            # Ensure datetime is timezone-aware (assume UTC if naive)
            if dt.tzinfo is None:
                dt = dt.replace(tzinfo=timezone.utc)
            
            timestamp_seconds = dt.timestamp()
            
            if as_milliseconds:
                return int(timestamp_seconds * 1000)
            else:
                return int(timestamp_seconds)
                
        except (ValueError, TypeError) as e:
            raise ValueError(f"Invalid datetime object: {dt}") from e
    
    @staticmethod
    def ensure_utc_datetime(dt: datetime) -> datetime:
        """
        Ensure datetime object is in UTC timezone.
        
        Args:
            dt: Datetime object
            
        Returns:
            datetime: UTC datetime object
        """
        if dt.tzinfo is None:
            # Naive datetime - assume it's already in UTC
            return dt.replace(tzinfo=timezone.utc)
        elif dt.tzinfo != timezone.utc:
            # Convert to UTC
            return dt.astimezone(timezone.utc)
        else:
            # Already UTC
            return dt
    
    @staticmethod
    def current_utc_timestamp(as_milliseconds: bool = True) -> int:
        """
        Get current UTC timestamp.
        
        Args:
            as_milliseconds: Whether to return timestamp in milliseconds (default: True)
            
        Returns:
            int: Current UTC timestamp
        """
        timestamp_seconds = time.time()
        
        if as_milliseconds:
            return int(timestamp_seconds * 1000)
        else:
            return int(timestamp_seconds)
    
    @staticmethod
    def validate_timestamp_range(start_timestamp: Union[int, float, str],
                                end_timestamp: Union[int, float, str],
                                is_milliseconds: bool = True) -> tuple[datetime, datetime]:
        """
        Validate and convert timestamp range to UTC datetime objects.
        
        Args:
            start_timestamp: Start timestamp
            end_timestamp: End timestamp
            is_milliseconds: Whether timestamps are in milliseconds (default: True)
            
        Returns:
            tuple[datetime, datetime]: Start and end UTC datetime objects
            
        Raises:
            ValueError: If timestamps are invalid or start > end
        """
        start_dt = TimestampConverter.timestamp_to_utc_datetime(
            start_timestamp, is_milliseconds
        )
        end_dt = TimestampConverter.timestamp_to_utc_datetime(
            end_timestamp, is_milliseconds
        )
        
        if start_dt >= end_dt:
            raise ValueError("Start timestamp must be before end timestamp")
        
        return start_dt, end_dt
    
    @staticmethod
    def convert_mt_timestamp_to_utc(mt_timestamp: Union[int, float],
                                   server_timezone_offset: Optional[int] = None) -> datetime:
        """
        Convert MT4/MT5 server timestamp to UTC datetime.
        
        MT4/MT5 servers may return timestamps in their local timezone.
        This function converts them to UTC.
        
        Args:
            mt_timestamp: Timestamp from MT4/MT5 server (in seconds)
            server_timezone_offset: Server timezone offset in hours from UTC (optional)
            
        Returns:
            datetime: UTC datetime object
        """
        try:
            # MT timestamps are typically in seconds
            dt = datetime.fromtimestamp(float(mt_timestamp), tz=timezone.utc)
            
            # If server timezone offset is provided, adjust accordingly
            if server_timezone_offset is not None:
                # Subtract the offset to get UTC (assuming MT timestamp is in server local time)
                from datetime import timedelta
                dt = dt - timedelta(hours=server_timezone_offset)
            
            return dt
            
        except (ValueError, TypeError, OSError) as e:
            raise ValueError(f"Invalid MT timestamp: {mt_timestamp}") from e


# Convenience functions for common operations
def ms_to_s(timestamp_ms: Union[int, float, str]) -> int:
    """Convert milliseconds to seconds timestamp."""
    return TimestampConverter.milliseconds_to_seconds(timestamp_ms)


def s_to_ms(timestamp_s: Union[int, float]) -> int:
    """Convert seconds to milliseconds timestamp."""
    return TimestampConverter.seconds_to_milliseconds(timestamp_s)


def to_utc_datetime(timestamp: Union[int, float, str], is_ms: bool = True) -> datetime:
    """Convert timestamp to UTC datetime."""
    return TimestampConverter.timestamp_to_utc_datetime(timestamp, is_ms)


def validate_range(start: Union[int, float, str], end: Union[int, float, str], 
                  is_ms: bool = True) -> tuple[datetime, datetime]:
    """Validate and convert timestamp range."""
    return TimestampConverter.validate_timestamp_range(start, end, is_ms)
