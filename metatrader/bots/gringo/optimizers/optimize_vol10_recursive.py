#!/usr/bin/env python3
"""
VOLATILITY 10 INDEX RECURSIVE OPTIMIZATION
Constant 5760 bars (4 days) M1 timeframe optimization with adaptive parameter adjustment
"""
import json
import subprocess
import re
import time
import random
from typing import Dict, List, Tuple, Any
import copy

# FIXED PARAMETERS
TIMEFRAME = "M1"
BACKTEST_COUNT = 5760
INITIAL_BALANCE = 50

# PARAMETER RANGES FOR VOLATILITY 10 INDEX (adjusted for lower volatility)
PARAM_RANGES = {
    # EMA parameters
    "fast_ema_period": (3, 15),
    "slow_ema_period": (10, 40),
    
    # RSI parameters
    "rsi_period": (5, 20),
    "rsi_oversold": (10, 30),
    "rsi_overbought": (70, 90),
    
    # Bollinger Bands
    "bb_period": (8, 25),
    "bb_deviation": (1.5, 3.5),
    
    # Trading parameters (adjusted for Vol10's lower volatility)
    "min_pip_move": (1.0, 20.0),
    "quick_profit_pips": (100, 400),
    "stop_loss_pips": (200, 1500),
    
    # Risk management
    "risk_per_trade_pct": (1.0, 5.0),
    "fixed_volume": (0.01, 1.0),
    "max_concurrent_trades": (1, 8),
    "max_daily_trades": (1000, 60000),
    "max_daily_drawdown_pct": (5.0, 25.0),
    "stop_trading_on_loss_pct": (2.0, 10.0),
    
    # Execution
    "min_trade_interval_ms": (200, 3000),
    "max_slippage_points": (1, 10),
    "simulated_spread": (0.00010, 0.0003)
}

class Vol10Optimizer:
    def __init__(self):
        self.best_score = -float('inf')
        self.best_config = None
        self.generation = 0
        self.results_history = []
        self.stagnation_count = 0
        self.max_stagnation = 25
        
    def generate_random_config(self) -> Dict:
        """Generate a random configuration within parameter ranges"""
        config = {
            "symbol": "Volatility 10 Index",
            "rate": 0.5,
            "remote_ticking": True,
            "currency_maps": [],
            "dry_run": False,
            "min_trade_interval_ms": random.randint(*PARAM_RANGES["min_trade_interval_ms"]),
            "scalping": {
                "timeframe": TIMEFRAME,
                "fast_ema_period": random.randint(*PARAM_RANGES["fast_ema_period"]),
                "slow_ema_period": random.randint(*PARAM_RANGES["slow_ema_period"]),
                "rsi_period": random.randint(*PARAM_RANGES["rsi_period"]),
                "rsi_oversold": random.randint(*PARAM_RANGES["rsi_oversold"]),
                "rsi_overbought": random.randint(*PARAM_RANGES["rsi_overbought"]),
                "bb_period": random.randint(*PARAM_RANGES["bb_period"]),
                "bb_deviation": round(random.uniform(*PARAM_RANGES["bb_deviation"]), 2),
                "min_pip_move": round(random.uniform(*PARAM_RANGES["min_pip_move"]), 2),
                "max_spread_pips": 0.0005,  # Keep constant for Vol10
                "quick_profit_pips": random.randint(*PARAM_RANGES["quick_profit_pips"]),
                "stop_loss_pips": random.randint(*PARAM_RANGES["stop_loss_pips"]),
                "risk_per_trade_pct": round(random.uniform(*PARAM_RANGES["risk_per_trade_pct"]), 2),
                "fixed_volume": round(random.uniform(*PARAM_RANGES["fixed_volume"]), 3)
            },
            "risk": {
                "max_concurrent_trades": random.randint(*PARAM_RANGES["max_concurrent_trades"]),
                "max_daily_drawdown_pct": round(random.uniform(*PARAM_RANGES["max_daily_drawdown_pct"]), 1),
                "max_daily_trades": random.randint(*PARAM_RANGES["max_daily_trades"]),
                "stop_trading_on_loss_pct": round(random.uniform(*PARAM_RANGES["stop_trading_on_loss_pct"]), 1)
            },
            "execution": {
                "max_slippage_points": random.randint(*PARAM_RANGES["max_slippage_points"]),
                "magic_base": 900000
            },
            "simulated": True,
            "initial_balance": INITIAL_BALANCE,
            "simulated_spread": round(random.uniform(*PARAM_RANGES["simulated_spread"]), 6),
            "backtest_count": BACKTEST_COUNT,
            "backtest_from": 0,
            "backtest_timeframe": TIMEFRAME
        }
        
        # Ensure fast EMA < slow EMA
        if config["scalping"]["fast_ema_period"] >= config["scalping"]["slow_ema_period"]:
            config["scalping"]["fast_ema_period"] = max(3, config["scalping"]["slow_ema_period"] - 2)
        
        # Ensure RSI bounds are logical
        if config["scalping"]["rsi_oversold"] >= config["scalping"]["rsi_overbought"]:
            config["scalping"]["rsi_oversold"] = min(25, config["scalping"]["rsi_overbought"] - 15)
        
        # Ensure stop loss > take profit for Vol10
        if config["scalping"]["stop_loss_pips"] <= config["scalping"]["quick_profit_pips"]:
            config["scalping"]["stop_loss_pips"] = config["scalping"]["quick_profit_pips"] + random.randint(100, 300)
        
        return config
    
    def mutate_config(self, base_config: Dict, mutation_rate: float = 0.3) -> Dict:
        """Mutate a configuration for genetic algorithm approach"""
        config = copy.deepcopy(base_config)
        
        # Randomly mutate parameters
        if random.random() < mutation_rate:
            config["scalping"]["fast_ema_period"] = random.randint(*PARAM_RANGES["fast_ema_period"])
        if random.random() < mutation_rate:
            config["scalping"]["slow_ema_period"] = random.randint(*PARAM_RANGES["slow_ema_period"])
        if random.random() < mutation_rate:
            config["scalping"]["rsi_period"] = random.randint(*PARAM_RANGES["rsi_period"])
        if random.random() < mutation_rate:
            config["scalping"]["rsi_oversold"] = random.randint(*PARAM_RANGES["rsi_oversold"])
        if random.random() < mutation_rate:
            config["scalping"]["rsi_overbought"] = random.randint(*PARAM_RANGES["rsi_overbought"])
        if random.random() < mutation_rate:
            config["scalping"]["bb_period"] = random.randint(*PARAM_RANGES["bb_period"])
        if random.random() < mutation_rate:
            config["scalping"]["bb_deviation"] = round(random.uniform(*PARAM_RANGES["bb_deviation"]), 2)
        if random.random() < mutation_rate:
            config["scalping"]["min_pip_move"] = round(random.uniform(*PARAM_RANGES["min_pip_move"]), 2)
        if random.random() < mutation_rate:
            config["scalping"]["quick_profit_pips"] = random.randint(*PARAM_RANGES["quick_profit_pips"])
        if random.random() < mutation_rate:
            config["scalping"]["stop_loss_pips"] = random.randint(*PARAM_RANGES["stop_loss_pips"])
        if random.random() < mutation_rate:
            config["scalping"]["risk_per_trade_pct"] = round(random.uniform(*PARAM_RANGES["risk_per_trade_pct"]), 2)
        if random.random() < mutation_rate:
            config["scalping"]["fixed_volume"] = round(random.uniform(*PARAM_RANGES["fixed_volume"]), 3)
        if random.random() < mutation_rate:
            config["risk"]["max_concurrent_trades"] = random.randint(*PARAM_RANGES["max_concurrent_trades"])
        if random.random() < mutation_rate:
            config["risk"]["max_daily_trades"] = random.randint(*PARAM_RANGES["max_daily_trades"])
        if random.random() < mutation_rate:
            config["min_trade_interval_ms"] = random.randint(*PARAM_RANGES["min_trade_interval_ms"])
        if random.random() < mutation_rate:
            config["simulated_spread"] = round(random.uniform(*PARAM_RANGES["simulated_spread"]), 6)
        
        # Apply constraints
        if config["scalping"]["fast_ema_period"] >= config["scalping"]["slow_ema_period"]:
            config["scalping"]["fast_ema_period"] = max(3, config["scalping"]["slow_ema_period"] - 2)
        
        if config["scalping"]["rsi_oversold"] >= config["scalping"]["rsi_overbought"]:
            config["scalping"]["rsi_oversold"] = min(25, config["scalping"]["rsi_overbought"] - 15)
        
        if config["scalping"]["stop_loss_pips"] <= config["scalping"]["quick_profit_pips"]:
            config["scalping"]["stop_loss_pips"] = config["scalping"]["quick_profit_pips"] + random.randint(100, 300)
        
        return config
    
    def update_config_file(self, config: Dict) -> None:
        """Update the env.vol10.json file"""
        with open('env.vol10.json', 'w') as f:
            json.dump(config, f, indent=2)
    
    def run_backtest(self) -> Dict[str, Any]:
        """Run a single backtest and extract results"""
        try:
            result = subprocess.run([
                'go', 'run', '.', 
                '-auth_token', 'test_terminal_token',
                '-id', 'vol10-recursive-opt',
                '-rpc_uri', '0.0.0.0:50050',
                '-ws_uri', 'ws://localhost:4010/bots',
                '-env_file', './env.vol10.json'
            ], capture_output=True, text=True, timeout=180)
            
            output = result.stdout + result.stderr
            
            # Extract metrics
            pnl_match = re.search(r'Total P&L: \$([-+]?\d+\.?\d*)', output)
            equity_match = re.search(r'End equity: \$([\d.]+)', output)
            drawdown_match = re.search(r'Max drawdown: \$([\d.]+)', output)
            trades_match = re.search(r'Trades closed: (\d+)', output)
            placed_match = re.search(r'Total trades placed: (\d+)', output)
            win_rate_match = re.search(r'Win rate: ([\d.]+)%', output)
            blown_match = re.search(r'Account status: (BLOWN OUT|ACTIVE)', output)
            max_wins_match = re.search(r'Max consecutive wins: (\d+)', output)
            max_losses_match = re.search(r'Max consecutive losses: (\d+)', output)
            avg_win_match = re.search(r'Average win: \$([\d.]+)', output)
            avg_loss_match = re.search(r'Average loss: \$-([\d.]+)', output)
            
            pnl = float(pnl_match.group(1)) if pnl_match else 0.0
            equity = float(equity_match.group(1)) if equity_match else INITIAL_BALANCE
            drawdown = float(drawdown_match.group(1)) if drawdown_match else 0.0
            trades_closed = int(trades_match.group(1)) if trades_match else 0
            trades_placed = int(placed_match.group(1)) if placed_match else 0
            win_rate = float(win_rate_match.group(1)) if win_rate_match else 0.0
            is_blown = blown_match.group(1) == "BLOWN OUT" if blown_match else False
            max_wins = int(max_wins_match.group(1)) if max_wins_match else 0
            max_losses = int(max_losses_match.group(1)) if max_losses_match else 0
            avg_win = float(avg_win_match.group(1)) if avg_win_match else 0.0
            avg_loss = float(avg_loss_match.group(1)) if avg_loss_match else 0.0

            # Calculate Vol10-specific score (adjusted for lower volatility characteristics)
            score = 0.0
            if not is_blown and trades_closed > 0:
                # Vol10 scoring: P&L (35%), Win Rate (30%), Activity (15%), Risk Control (15%), Consistency (5%)
                pnl_score = pnl * 0.35
                win_rate_score = win_rate * 0.30
                activity_score = min(trades_closed / 150.0, 2.5) * 40 * 0.15  # Adjusted for Vol10's pace
                risk_score = max(0, (INITIAL_BALANCE - drawdown) / INITIAL_BALANCE * 100) * 0.15
                consistency_score = (max_wins * 2.5 - max_losses * 1.5) * 0.05

                score = pnl_score + win_rate_score + activity_score + risk_score + consistency_score
            elif is_blown:
                score = -1500.0  # Heavy penalty for blown accounts

            return {
                'pnl': pnl, 'equity': equity, 'drawdown': drawdown,
                'trades_closed': trades_closed, 'trades_placed': trades_placed,
                'win_rate': win_rate, 'is_blown': is_blown,
                'max_wins': max_wins, 'max_losses': max_losses,
                'avg_win': avg_win, 'avg_loss': avg_loss,
                'score': score, 'success': True
            }
        except Exception as e:
            return {
                'pnl': -INITIAL_BALANCE, 'equity': 0.0, 'drawdown': INITIAL_BALANCE,
                'trades_closed': 0, 'trades_placed': 0, 'win_rate': 0.0,
                'is_blown': True, 'max_wins': 0, 'max_losses': 100,
                'avg_win': 0.0, 'avg_loss': 0.0,
                'score': -2500.0, 'success': False, 'error': str(e)
            }

    def optimize_recursive(self, max_generations: int = 400, population_size: int = 18):
        """Main recursive optimization loop"""
        print(f"🚀 VOLATILITY 10 INDEX RECURSIVE OPTIMIZATION")
        print(f"Fixed Parameters: {TIMEFRAME} timeframe, {BACKTEST_COUNT} bars (4 days)")
        print(f"Initial Balance: ${INITIAL_BALANCE}")
        print(f"Max Generations: {max_generations}, Population Size: {population_size}")
        print("="*80)

        # Initialize population
        population = []
        for i in range(population_size):
            config = self.generate_random_config()
            self.update_config_file(config)
            result = self.run_backtest()

            population.append({
                'config': config,
                'result': result,
                'score': result['score']
            })

            print(f"Init [{i+1}/{population_size}] Score: {result['score']:.1f} | "
                  f"P&L: ${result['pnl']:.2f} | Trades: {result['trades_closed']} | "
                  f"Win%: {result['win_rate']:.1f} | {'BLOWN' if result['is_blown'] else 'ACTIVE'}")

            if result['score'] > self.best_score:
                self.best_score = result['score']
                self.best_config = config
                self.stagnation_count = 0

            time.sleep(0.1)

        # Sort population by score
        population.sort(key=lambda x: x['score'], reverse=True)

        # Main optimization loop
        for generation in range(max_generations):
            self.generation = generation + 1

            print(f"\n🧬 GENERATION {self.generation}")
            print(f"Best Score: {self.best_score:.1f} | Stagnation: {self.stagnation_count}")

            # Create new generation
            new_population = []

            # Keep top 25% (elitism - higher for Vol10 stability)
            elite_count = max(1, population_size // 4)
            for i in range(elite_count):
                new_population.append(population[i])

            # Generate offspring through mutation
            while len(new_population) < population_size:
                # Select parent (tournament selection)
                parent1 = random.choice(population[:population_size//2])

                # Mutate parent with adaptive rate
                mutation_rate = 0.25 + (self.stagnation_count * 0.015)  # Lower base rate for Vol10
                child_config = self.mutate_config(parent1['config'], mutation_rate)

                # Test child
                self.update_config_file(child_config)
                child_result = self.run_backtest()

                new_population.append({
                    'config': child_config,
                    'result': child_result,
                    'score': child_result['score']
                })

                # Check for improvement
                if child_result['score'] > self.best_score:
                    self.best_score = child_result['score']
                    self.best_config = child_config
                    self.stagnation_count = 0
                    print(f"🎯 NEW BEST! Score: {child_result['score']:.1f} | "
                          f"P&L: ${child_result['pnl']:.2f} | Win%: {child_result['win_rate']:.1f}")

                time.sleep(0.05)

            # Sort new population
            new_population.sort(key=lambda x: x['score'], reverse=True)
            population = new_population

            # Check stagnation
            if self.stagnation_count > 0:
                self.stagnation_count += 1
            else:
                self.stagnation_count = 1

            # Display generation results
            best_gen = population[0]
            print(f"Gen Best: Score {best_gen['score']:.1f} | P&L ${best_gen['result']['pnl']:.2f} | "
                  f"Trades {best_gen['result']['trades_closed']} | Win% {best_gen['result']['win_rate']:.1f}")

            # Adaptive parameter adjustment if stagnant
            if self.stagnation_count > self.max_stagnation:
                print(f"🔄 STAGNATION DETECTED! Expanding search space...")
                self.expand_search_space()
                self.stagnation_count = 0

            # Early termination if excellent result found (adjusted for Vol10)
            if self.best_score > 80 and population[0]['result']['pnl'] > 15:
                print(f"🏆 EXCELLENT RESULT FOUND! Terminating early.")
                break

        # Final results
        print(f"\n" + "="*80)
        print(f"🏆 OPTIMIZATION COMPLETE!")
        print(f"Best Score: {self.best_score:.1f}")
        print(f"Generations: {self.generation}")

        if self.best_config:
            self.update_config_file(self.best_config)
            final_result = self.run_backtest()

            print(f"\n🎯 FINAL BEST CONFIGURATION:")
            print(f"P&L: ${final_result['pnl']:.2f}")
            print(f"Win Rate: {final_result['win_rate']:.1f}%")
            print(f"Trades: {final_result['trades_closed']}/{final_result['trades_placed']}")
            print(f"Max DD: ${final_result['drawdown']:.2f}")
            print(f"Streaks: {final_result['max_wins']}/{final_result['max_losses']}")
            print(f"Account: {'BLOWN' if final_result['is_blown'] else 'ACTIVE'}")

            print(f"\n📊 OPTIMAL PARAMETERS:")
            scalping = self.best_config['scalping']
            print(f"EMA: {scalping['fast_ema_period']}/{scalping['slow_ema_period']}")
            print(f"RSI: {scalping['rsi_period']}({scalping['rsi_oversold']}/{scalping['rsi_overbought']})")
            print(f"BB: {scalping['bb_period']}, {scalping['bb_deviation']}")
            print(f"TP/SL: {scalping['quick_profit_pips']}/{scalping['stop_loss_pips']}")
            print(f"Volume: {scalping['fixed_volume']}")
            print(f"Risk: {scalping['risk_per_trade_pct']}%")
            print(f"Min Pip Move: {scalping['min_pip_move']}")
            print(f"Trade Interval: {self.best_config['min_trade_interval_ms']}ms")
            print(f"Spread: {self.best_config['simulated_spread']}")
        else:
            print("❌ No viable configuration found!")

    def expand_search_space(self):
        """Expand parameter ranges when stagnant"""
        global PARAM_RANGES

        # Expand ranges by 15% (more conservative for Vol10)
        for param, (min_val, max_val) in PARAM_RANGES.items():
            if isinstance(min_val, int):
                expansion = max(1, int((max_val - min_val) * 0.15))
                PARAM_RANGES[param] = (max(1, min_val - expansion), max_val + expansion)
            else:
                expansion = (max_val - min_val) * 0.15
                PARAM_RANGES[param] = (max(0.01, min_val - expansion), max_val + expansion)

        print("🔄 Search space expanded for Vol10!")

def main():
    print("🎯 Starting Volatility 10 Index Optimization")
    print("Timeframe: M1 (1-minute)")
    print("Backtest Period: 5760 bars (~4 days)")
    print("Strategy: Adaptive genetic algorithm with elitism")
    print("-" * 60)

    optimizer = Vol10Optimizer()
    optimizer.optimize_recursive(max_generations=350, population_size=16)

    print("\n✅ Optimization completed!")
    print("📁 Results saved to env.vol10.json")
    print("🚀 Ready for live trading or further testing")

if __name__ == "__main__":
    main()
