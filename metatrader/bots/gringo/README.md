# Gringo Scalping Bot

A high-frequency scalping bot implementing multiple proven strategies for 1-5 minute timeframes.

## Strategies Implemented

### 1. EMA Crossover with RSI Filter
- **Fast EMA (5)** crossing above/below **Slow EMA (21)**
- **RSI confirmation**: Oversold (40) for longs, Overbought (60) for shorts
- Designed for trend-following entries with momentum confirmation

### 2. Bollinger Band Mean Reversion
- Price touching or near **Bollinger Bands (20, 2.0)**
- Combined with RSI extremes for high-probability reversals
- Targets quick moves back to the middle band

### 3. Price Action Momentum
- **3-bar momentum** detection for breakout entries
- Minimum pip movement filter to avoid noise
- Quick entries on strong directional moves

## Key Features

- **High Win Rate Focus**: Tight stops (10 pips), quick profits (5 pips)
- **Spread Filtering**: Only trades when spread < 2 pips
- **Risk Management**: Max daily trades, drawdown limits, time windows
- **Multiple Timeframes**: M1 (default) or M5 scalping
- **Position Sizing**: Risk-based volume calculation

## Configuration

### Scalping Parameters
```json
"scalping": {
  "timeframe": "M1",           // M1 or M5
  "fast_ema_period": 5,        // Fast EMA period
  "slow_ema_period": 21,       // Slow EMA period
  "rsi_period": 14,            // RSI calculation period
  "rsi_oversold": 30,          // RSI oversold level
  "rsi_overbought": 70,        // RSI overbought level
  "bb_period": 20,             // Bollinger Bands period
  "bb_deviation": 2.0,         // BB standard deviation
  "min_pip_move": 0.0005,      // Min movement for momentum (0.5 pips)
  "max_spread_pips": 0.0002,   // Max spread to trade (2 pips)
  "quick_profit_pips": 5,      // Take profit in pips
  "stop_loss_pips": 10,        // Stop loss in pips
  "risk_per_trade_pct": 1.0    // Risk per trade %
}
```

### Risk Management
```json
"risk": {
  "max_concurrent_trades": 3,     // Max open positions
  "max_daily_drawdown_pct": 5.0,  // Stop trading at 5% daily DD
  "max_daily_trades": 50,         // Max trades per day
  "time_windows": ["09:00-17:00"], // Trading hours
  "stop_trading_on_loss_pct": 3.0 // Stop at 3% loss
}
```

## Usage

### Live Trading
```bash
./gringo -id gringo_eur -rpc_uri localhost:50051 -ws_uri ws://localhost:8080 -auth_token YOUR_TOKEN -env_file ./env.json
```

### Backtesting
Create `env_backtest.json`:
```json
{
  "symbol": "EURUSD",
  "rate": 0.05,
  "remote_ticking": false,
  "simulated": true,
  "initial_balance": 10000,
  "simulated_spread": 0.00015,
  "backtest_from": 1716940800000,
  "backtest_count": 5000,
  "backtest_timeframe": "M1",
  "dry_run": false,
  "scalping": {
    "timeframe": "M1",
    "quick_profit_pips": 3,
    "stop_loss_pips": 8
  }
}
```

Run backtest:
```bash
./gringo -id gringo_bt -rpc_uri localhost:50051 -ws_uri ws://localhost:8080 -auth_token YOUR_TOKEN -env_file ./env_backtest.json
```

## Strategy Logic

### Entry Conditions

**LONG Signals:**
1. Fast EMA > Slow EMA AND RSI < 40 (trend + oversold)
2. Price near BB lower band AND RSI < 40 (mean reversion)
3. Strong bullish momentum (3-bar) AND RSI < 60 (breakout)

**SHORT Signals:**
1. Fast EMA < Slow EMA AND RSI > 60 (trend + overbought)
2. Price near BB upper band AND RSI > 60 (mean reversion)
3. Strong bearish momentum (3-bar) AND RSI > 40 (breakout)

### Exit Strategy
- **Take Profit**: 5 pips (configurable)
- **Stop Loss**: 10 pips (configurable)
- **Risk/Reward**: 1:0.5 ratio optimized for high win rate

### Risk Controls
- Spread filtering (max 2 pips)
- Daily trade limits
- Drawdown protection
- Time window restrictions
- Position size limits

## Performance Expectations

- **Win Rate**: Target 70-80% (tight stops, quick profits)
- **Risk/Reward**: 1:0.5 (compensated by high win rate)
- **Trades/Day**: 20-50 depending on volatility
- **Best Pairs**: EURUSD, GBPUSD, USDJPY (low spread majors)
- **Best Times**: London/NY overlap (high liquidity)

## Optimization Tips

1. **Spread Sensitive**: Use during low-spread periods
2. **Volatility Dependent**: Adjust pip targets based on ATR
3. **Time-of-Day**: Best during active trading sessions
4. **Pair Selection**: Focus on major pairs with tight spreads
5. **Risk Management**: Never risk more than 1% per trade

## Monitoring

The bot logs all signals and trades. Key metrics to watch:
- Win rate (should be >70%)
- Average trade duration (should be <30 minutes)
- Daily drawdown
- Spread conditions
- Signal frequency

## Disclaimer

Scalping is high-risk trading. This bot is for educational purposes. Always test thoroughly in simulation before live trading. Past performance does not guarantee future results.
