{"symbol": "Volatility 75 Index", "bot_name": "gringo", "bot_path": "../gringo", "config_file": "env.vol75.json", "timeframes": ["M1", "M5", "M15"], "max_generations": 300, "population_size": 16, "initial_balance": 50, "backtest_count": 5760, "mutation_rate": 0.4, "elite_percentage": 0.25, "stagnation_limit": 20, "parameter_ranges": {"fast_ema_period": {"min": 2, "max": 25, "is_int": true}, "slow_ema_period": {"min": 8, "max": 60, "is_int": true}, "rsi_period": {"min": 3, "max": 30, "is_int": true}, "rsi_oversold": {"min": 5, "max": 40, "is_int": true}, "rsi_overbought": {"min": 60, "max": 95, "is_int": true}, "bb_period": {"min": 5, "max": 35, "is_int": true}, "bb_deviation": {"min": 1.0, "max": 5.0, "is_int": false}, "min_pip_move": {"min": 0.5, "max": 15.0, "is_int": false}, "quick_profit_pips": {"min": 20, "max": 800, "is_int": true}, "stop_loss_pips": {"min": 50, "max": 1200, "is_int": true}, "risk_per_trade_pct": {"min": 0.3, "max": 8.0, "is_int": false}, "fixed_volume": {"min": 0.05, "max": 3.0, "is_int": false}, "max_concurrent_trades": {"min": 1, "max": 15, "is_int": true}, "max_daily_trades": {"min": 50, "max": 100000, "is_int": true}, "max_daily_drawdown_pct": {"min": 5.0, "max": 60.0, "is_int": false}, "stop_trading_on_loss_pct": {"min": 1.0, "max": 20.0, "is_int": false}, "min_trade_interval_ms": {"min": 50, "max": 8000, "is_int": true}, "max_slippage_points": {"min": 1, "max": 30, "is_int": true}, "simulated_spread": {"min": 3e-05, "max": 0.01, "is_int": false}}}