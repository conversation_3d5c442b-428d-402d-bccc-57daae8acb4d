package bridge

import "context"

type Options struct {
	Id        string
	RpcUri    string
	WsUri     string
	Token     string
	Client    BridgeRpcServiceClient
	Ctx       context.Context
	Simulated bool
}

type BotState struct {
	Is_paused bool `json:"is_paused"`
}

// Callback now returns a per-tick in-memory log buffer. Bridge stores it.
type Callback func(Options, *TickType) []string

type Map map[string]interface{}

type WebSocketData struct {
	Config map[string]interface{} `json:"config"`
	State  BotState               `json:"state"`
	Id     string                 `json:"id"`
}

type GenericWsMessage struct {
	Event string      `json:"event"`
	Data  interface{} `json:"data"`
}

// LogsMessage is used for sending logs over WebSocket
type LogsMessage struct {
	Id   string   `json:"id"`
	Logs []string `json:"logs"`
}
