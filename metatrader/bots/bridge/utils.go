package bridge

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"
)

func toTitleCase(s string) string {
	if len(s) == 0 {
		return s
	}
	return strings.ToUpper(string(s[0])) + s[1:]
}

func MapToStruct(m map[string]interface{}, s interface{}) error {
	structValue := reflect.ValueOf(s).Elem()

	for key, value := range m {
		structFieldName := toTitleCase(key)
		field := structValue.FieldByName(structFieldName)
		if !field.IsValid() {
			continue // Skip if the field doesn't exist
		}
		if !field.CanSet() {
			return fmt.Errorf("cannot set field %s", structFieldName)
		}

		// nil values -> set zero for pointer/interface/map/slice, otherwise error
		if value == nil {
			switch field.Kind() {
			case reflect.Ptr, reflect.Interface, reflect.Map, reflect.Slice:
				field.Set(reflect.Zero(field.Type()))
				continue
			default:
				return fmt.Errorf("cannot assign nil to field %s of type %s", structFieldName, field.Type())
			}
		}

		// Nested map -> recurse into struct or *struct
		if mv, ok := value.(map[string]interface{}); ok {
			switch field.Kind() {
			case reflect.Struct:
				if err := MapToStruct(mv, field.Addr().Interface()); err != nil {
					return fmt.Errorf("error mapping field %s: %v", structFieldName, err)
				}
				continue
			case reflect.Ptr:
				if field.Type().Elem().Kind() == reflect.Struct {
					if field.IsNil() {
						field.Set(reflect.New(field.Type().Elem()))
					}
					if err := MapToStruct(mv, field.Interface()); err != nil {
						return fmt.Errorf("error mapping field %s: %v", structFieldName, err)
					}
					continue
				}
			}
		}

		// Handle slices (e.g., []string, []int, []float64)
		if field.Kind() == reflect.Slice {
			if arr, ok := value.([]interface{}); ok {
				elemType := field.Type().Elem()
				slice := reflect.MakeSlice(field.Type(), 0, len(arr))
				for _, item := range arr {
					ev := reflect.New(elemType).Elem()
					if err := setWithCoercion(ev, item); err != nil {
						return fmt.Errorf("field %s slice element: %w", structFieldName, err)
					}
					slice = reflect.Append(slice, ev)
				}
				field.Set(slice)
				continue
			}
		}

		// Set scalar with coercion
		if err := setWithCoercion(field, value); err != nil {
			return fmt.Errorf("field %s: %w", structFieldName, err)
		}
	}
	return nil
}

// setWithCoercion attempts to coerce common JSON-decoded types (float64, string, bool)
// into the destination field's kind.
func setWithCoercion(field reflect.Value, value interface{}) error {
	// If types are directly assignable
	v := reflect.ValueOf(value)
	if v.IsValid() && v.Type().AssignableTo(field.Type()) {
		field.Set(v)
		return nil
	}

	switch field.Kind() {
	case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
		var iv int64
		switch x := value.(type) {
		case float64:
			iv = int64(x)
		case float32:
			iv = int64(x)
		case int:
			iv = int64(x)
		case int8:
			iv = int64(x)
		case int16:
			iv = int64(x)
		case int32:
			iv = int64(x)
		case int64:
			iv = x
		case uint, uint8, uint16, uint32, uint64:
			iv = int64(reflect.ValueOf(x).Uint())
		case bool:
			if x {
				iv = 1
			} else {
				iv = 0
			}
		case string:
			if x == "" {
				iv = 0
			} else {
				n, err := strconv.ParseInt(x, 10, 64)
				if err != nil {
					return fmt.Errorf("cannot parse '%s' as int: %w", x, err)
				}
				iv = n
			}
		default:
			// Try generic conversion
			if v.IsValid() && v.Type().ConvertibleTo(field.Type()) {
				field.Set(v.Convert(field.Type()))
				return nil
			}
			return fmt.Errorf("unsupported conversion %T -> %s", value, field.Type())
		}
		field.SetInt(iv)
		return nil

	case reflect.Float32, reflect.Float64:
		var fv float64
		switch x := value.(type) {
		case float64:
			fv = x
		case float32:
			fv = float64(x)
		case int, int8, int16, int32, int64:
			fv = float64(reflect.ValueOf(x).Int())
		case uint, uint8, uint16, uint32, uint64:
			fv = float64(reflect.ValueOf(x).Uint())
		case bool:
			if x {
				fv = 1
			} else {
				fv = 0
			}
		case string:
			if x == "" {
				fv = 0
			} else {
				n, err := strconv.ParseFloat(x, 64)
				if err != nil {
					return fmt.Errorf("cannot parse '%s' as float: %w", x, err)
				}
				fv = n
			}
		default:
			if v.IsValid() && v.Type().ConvertibleTo(field.Type()) {
				field.Set(v.Convert(field.Type()))
				return nil
			}
			return fmt.Errorf("unsupported conversion %T -> %s", value, field.Type())
		}
		if field.Kind() == reflect.Float32 {
			field.SetFloat(float64(float32(fv)))
		} else {
			field.SetFloat(fv)
		}
		return nil

	case reflect.Bool:
		var bv bool
		switch x := value.(type) {
		case bool:
			bv = x
		case float64:
			bv = x != 0
		case float32:
			bv = x != 0
		case int, int8, int16, int32, int64:
			bv = reflect.ValueOf(x).Int() != 0
		case uint, uint8, uint16, uint32, uint64:
			bv = reflect.ValueOf(x).Uint() != 0
		case string:
			lx := strings.ToLower(strings.TrimSpace(x))
			if lx == "true" || lx == "1" || lx == "yes" || lx == "y" {
				bv = true
			} else if lx == "false" || lx == "0" || lx == "no" || lx == "n" {
				bv = false
			} else {
				return fmt.Errorf("cannot parse '%s' as bool", x)
			}
		default:
			return fmt.Errorf("unsupported conversion %T -> bool", value)
		}
		field.SetBool(bv)
		return nil

	case reflect.String:
		switch x := value.(type) {
		case string:
			field.SetString(x)
			return nil
		case float64, float32, int, int8, int16, int32, int64, uint, uint8, uint16, uint32, uint64, bool:
			field.SetString(fmt.Sprint(x))
			return nil
		default:
			return fmt.Errorf("unsupported conversion %T -> string", value)
		}

	case reflect.Ptr:
		// Handle pointer to scalar types
		elem := field.Type().Elem()
		nv := reflect.New(elem).Elem()
		if err := setWithCoercion(nv, value); err != nil {
			return err
		}
		p := reflect.New(elem)
		p.Elem().Set(nv)
		field.Set(p)
		return nil
	}

	// Last resort: try Convert if possible
	if v.IsValid() && v.Type().ConvertibleTo(field.Type()) {
		field.Set(v.Convert(field.Type()))
		return nil
	}
	return fmt.Errorf("unsupported conversion %T -> %s", value, field.Type())
}
