package shared

import (
	. "bridge"
)

type TradeCopyMessage struct {
	Position     *AccountPositionType `json:"position"`
	Message_type MessageType          `json:"message_type"`
	// New: master account snapshot for sizing logic on slaves
	MasterEquity  float32 `json:"master_equity,omitempty"`
	MasterBalance float32 `json:"master_balance,omitempty"`
}

type MessageType int

const (
	NEW MessageType = iota
	CLOSE
	MODIFY
)
