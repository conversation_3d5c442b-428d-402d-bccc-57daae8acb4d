package main

import (
	"encoding/json"
	"fmt"
	"math"
	"math/rand"
	"os"
	"os/exec"
	"path/filepath"
	"regexp"
	"strconv"
	"strings"
)

// OptimizerConfig holds the configuration for the optimizer
type OptimizerConfig struct {
	Symbol          string           `json:"symbol"`
	BotName         string           `json:"bot_name"`
	BotPath         string           `json:"bot_path"`
	ConfigFile      string           `json:"config_file"`
	Timeframes      []string         `json:"timeframes"`
	MaxGenerations  int              `json:"max_generations"`
	PopulationSize  int              `json:"population_size"`
	InitialBalance  float64          `json:"initial_balance"`
	BacktestCount   int              `json:"backtest_count"`
	ParameterRanges map[string]Range `json:"parameter_ranges"`
	MutationRate    float64          `json:"mutation_rate"`
	ElitePercentage float64          `json:"elite_percentage"`
	StagnationLimit int              `json:"stagnation_limit"`
}

// Range defines min/max values for parameters
type Range struct {
	Min   float64 `json:"min"`
	Max   float64 `json:"max"`
	IsInt bool    `json:"is_int"`
}

// OptimizationResult holds the results of a single test
type OptimizationResult struct {
	Config       map[string]interface{} `json:"config"`
	PnL          float64                `json:"pnl"`
	WinRate      float64                `json:"win_rate"`
	TradesClosed int                    `json:"trades_closed"`
	TradesPlaced int                    `json:"trades_placed"`
	MaxDrawdown  float64                `json:"max_drawdown"`
	IsBlown      bool                   `json:"is_blown"`
	MaxWins      int                    `json:"max_wins"`
	MaxLosses    int                    `json:"max_losses"`
	AvgWin       float64                `json:"avg_win"`
	AvgLoss      float64                `json:"avg_loss"`
	Score        float64                `json:"score"`
	Timeframe    string                 `json:"timeframe"`
	Generation   int                    `json:"generation"`
}

// Optimizer is the main optimization engine
type Optimizer struct {
	Config           OptimizerConfig
	BestResult       *OptimizationResult
	Generation       int
	StagnationCount  int
	CurrentTimeframe string
	TimeframeIndex   int
	Results          []OptimizationResult
}

// NewOptimizer creates a new optimizer instance
func NewOptimizer(configPath string) (*Optimizer, error) {
	var config OptimizerConfig

	// Load optimizer configuration
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read optimizer config: %v", err)
	}

	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("failed to parse optimizer config: %v", err)
	}

	// Set defaults
	if len(config.Timeframes) == 0 {
		config.Timeframes = []string{"M1", "M5", "M15", "M30"}
	}
	if config.MaxGenerations == 0 {
		config.MaxGenerations = 300
	}
	if config.PopulationSize == 0 {
		config.PopulationSize = 20
	}
	if config.InitialBalance == 0 {
		config.InitialBalance = 50
	}
	if config.BacktestCount == 0 {
		config.BacktestCount = 5760
	}
	if config.MutationRate == 0 {
		config.MutationRate = 0.3
	}
	if config.ElitePercentage == 0 {
		config.ElitePercentage = 0.2
	}
	if config.StagnationLimit == 0 {
		config.StagnationLimit = 25
	}

	optimizer := &Optimizer{
		Config:           config,
		CurrentTimeframe: config.Timeframes[0],
		TimeframeIndex:   0,
		Results:          make([]OptimizationResult, 0),
	}

	return optimizer, nil
}

// generateRandomConfig creates a random configuration within parameter ranges
func (o *Optimizer) generateRandomConfig() map[string]interface{} {
	config := make(map[string]interface{})

	for param, paramRange := range o.Config.ParameterRanges {
		var value interface{}
		if paramRange.IsInt {
			value = rand.Intn(int(paramRange.Max-paramRange.Min+1)) + int(paramRange.Min)
		} else {
			value = rand.Float64()*(paramRange.Max-paramRange.Min) + paramRange.Min
		}
		config[param] = value
	}

	// Apply constraints based on parameter relationships
	o.applyConstraints(config)

	return config
}

// applyConstraints ensures parameter relationships are valid
func (o *Optimizer) applyConstraints(config map[string]interface{}) {
	// Ensure fast EMA < slow EMA
	if fastEMA, ok := config["fast_ema_period"]; ok {
		if slowEMA, ok := config["slow_ema_period"]; ok {
			if fastEMA.(int) >= slowEMA.(int) {
				config["fast_ema_period"] = max(2, slowEMA.(int)-1)
			}
		}
	}

	// Ensure RSI oversold < overbought
	if oversold, ok := config["rsi_oversold"]; ok {
		if overbought, ok := config["rsi_overbought"]; ok {
			if oversold.(int) >= overbought.(int) {
				config["rsi_oversold"] = min(30, overbought.(int)-10)
			}
		}
	}

	// Ensure stop loss > take profit for volatile instruments
	if tp, ok := config["quick_profit_pips"]; ok {
		if sl, ok := config["stop_loss_pips"]; ok {
			if sl.(int) <= tp.(int) {
				config["stop_loss_pips"] = tp.(int) + rand.Intn(200) + 50
			}
		}
	}
}

// mutateConfig creates a mutated version of a configuration
func (o *Optimizer) mutateConfig(baseConfig map[string]interface{}) map[string]interface{} {
	config := make(map[string]interface{})

	// Copy base config
	for k, v := range baseConfig {
		config[k] = v
	}

	// Mutate parameters based on mutation rate
	for param, paramRange := range o.Config.ParameterRanges {
		if rand.Float64() < o.Config.MutationRate {
			var value interface{}
			if paramRange.IsInt {
				value = rand.Intn(int(paramRange.Max-paramRange.Min+1)) + int(paramRange.Min)
			} else {
				value = rand.Float64()*(paramRange.Max-paramRange.Min) + paramRange.Min
			}
			config[param] = value
		}
	}

	// Apply constraints
	o.applyConstraints(config)

	return config
}

// updateConfigFile updates the bot's configuration file
func (o *Optimizer) updateConfigFile(config map[string]interface{}) error {
	// Load existing config file
	var existingConfig map[string]interface{}

	configPath := filepath.Join(o.Config.BotPath, o.Config.ConfigFile)
	data, err := os.ReadFile(configPath)
	if err != nil {
		return fmt.Errorf("failed to read config file: %v", err)
	}

	if err := json.Unmarshal(data, &existingConfig); err != nil {
		return fmt.Errorf("failed to parse config file: %v", err)
	}

	// Update parameters
	for param, value := range config {
		o.setNestedValue(existingConfig, param, value)
	}

	// Set timeframe and backtest settings
	o.setNestedValue(existingConfig, "scalping.timeframe", o.CurrentTimeframe)
	o.setNestedValue(existingConfig, "backtest_timeframe", o.CurrentTimeframe)
	o.setNestedValue(existingConfig, "backtest_count", o.Config.BacktestCount)
	o.setNestedValue(existingConfig, "backtest_from", 0)
	o.setNestedValue(existingConfig, "simulated", true)
	o.setNestedValue(existingConfig, "initial_balance", o.Config.InitialBalance)
	o.setNestedValue(existingConfig, "symbol", o.Config.Symbol)

	// Write updated config
	updatedData, err := json.MarshalIndent(existingConfig, "", "  ")
	if err != nil {
		return fmt.Errorf("failed to marshal config: %v", err)
	}

	return os.WriteFile(configPath, updatedData, 0644)
}

// setNestedValue sets a nested value in a map using dot notation
func (o *Optimizer) setNestedValue(config map[string]interface{}, key string, value interface{}) {
	keys := strings.Split(key, ".")
	current := config

	for _, k := range keys[:len(keys)-1] {
		if _, exists := current[k]; !exists {
			current[k] = make(map[string]interface{})
		}
		if nested, ok := current[k].(map[string]interface{}); ok {
			current = nested
		} else {
			// Convert to map if it's not already
			current[k] = make(map[string]interface{})
			current = current[k].(map[string]interface{})
		}
	}

	finalKey := keys[len(keys)-1]
	current[finalKey] = value
}

// runBacktest executes a single backtest and extracts results
func (o *Optimizer) runBacktest() (*OptimizationResult, error) {
	// Run the bot
	cmd := exec.Command("go", "run", ".",
		"-auth_token", "test_terminal_token",
		"-id", fmt.Sprintf("%s-optimizer", o.Config.BotName),
		"-rpc_uri", "0.0.0.0:50050",
		"-ws_uri", "ws://localhost:4010/bots",
		"-env_file", o.Config.ConfigFile)

	cmd.Dir = o.Config.BotPath

	output, err := cmd.CombinedOutput()
	if err != nil {
		return &OptimizationResult{
			PnL:        -o.Config.InitialBalance,
			IsBlown:    true,
			Score:      -1000.0,
			Timeframe:  o.CurrentTimeframe,
			Generation: o.Generation,
		}, nil
	}

	// Parse output
	result := o.parseBacktestOutput(string(output))
	result.Timeframe = o.CurrentTimeframe
	result.Generation = o.Generation

	return result, nil
}

// parseBacktestOutput extracts metrics from backtest output
func (o *Optimizer) parseBacktestOutput(output string) *OptimizationResult {
	result := &OptimizationResult{}

	// Extract metrics using regex
	patterns := map[string]*regexp.Regexp{
		"pnl":           regexp.MustCompile(`Total P&L: \$([-+]?\d+\.?\d*)`),
		"win_rate":      regexp.MustCompile(`Win rate: ([\d.]+)%`),
		"trades_closed": regexp.MustCompile(`Trades closed: (\d+)`),
		"trades_placed": regexp.MustCompile(`Total trades placed: (\d+)`),
		"max_drawdown":  regexp.MustCompile(`Max drawdown: \$([\d.]+)`),
		"blown":         regexp.MustCompile(`Account status: (BLOWN OUT|ACTIVE)`),
		"max_wins":      regexp.MustCompile(`Max consecutive wins: (\d+)`),
		"max_losses":    regexp.MustCompile(`Max consecutive losses: (\d+)`),
		"avg_win":       regexp.MustCompile(`Average win: \$([\d.]+)`),
		"avg_loss":      regexp.MustCompile(`Average loss: \$-([\d.]+)`),
	}

	for metric, pattern := range patterns {
		if matches := pattern.FindStringSubmatch(output); len(matches) > 1 {
			switch metric {
			case "pnl":
				result.PnL, _ = strconv.ParseFloat(matches[1], 64)
			case "win_rate":
				result.WinRate, _ = strconv.ParseFloat(matches[1], 64)
			case "trades_closed":
				result.TradesClosed, _ = strconv.Atoi(matches[1])
			case "trades_placed":
				result.TradesPlaced, _ = strconv.Atoi(matches[1])
			case "max_drawdown":
				result.MaxDrawdown, _ = strconv.ParseFloat(matches[1], 64)
			case "blown":
				result.IsBlown = matches[1] == "BLOWN OUT"
			case "max_wins":
				result.MaxWins, _ = strconv.Atoi(matches[1])
			case "max_losses":
				result.MaxLosses, _ = strconv.Atoi(matches[1])
			case "avg_win":
				result.AvgWin, _ = strconv.ParseFloat(matches[1], 64)
			case "avg_loss":
				result.AvgLoss, _ = strconv.ParseFloat(matches[1], 64)
			}
		}
	}

	// Calculate score
	result.Score = o.calculateScore(result)

	return result
}

// calculateScore computes optimization score based on multiple factors
func (o *Optimizer) calculateScore(result *OptimizationResult) float64 {
	if result.IsBlown || result.TradesClosed == 0 {
		return -1000.0
	}

	// Multi-factor scoring: P&L (40%), Win Rate (25%), Activity (15%), Risk Control (15%), Consistency (5%)
	pnlScore := result.PnL * 0.4
	winRateScore := result.WinRate * 0.25
	activityScore := math.Min(float64(result.TradesClosed)/100.0, 3.0) * 50 * 0.15
	riskScore := math.Max(0, (o.Config.InitialBalance-result.MaxDrawdown)/o.Config.InitialBalance*100) * 0.15
	consistencyScore := (float64(result.MaxWins)*3 - float64(result.MaxLosses)*2) * 0.05

	return pnlScore + winRateScore + activityScore + riskScore + consistencyScore
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}
