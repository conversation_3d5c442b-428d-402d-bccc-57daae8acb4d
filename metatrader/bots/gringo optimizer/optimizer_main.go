package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"log"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"sort"
	"strings"
	"time"
)

// switchTimeframe switches to the next timeframe if current one fails
func (o *Optimizer) switchTimeframe() {
	o.TimeframeIndex = (o.TimeframeIndex + 1) % len(o.Config.Timeframes)
	o.CurrentTimeframe = o.Config.Timeframes[o.TimeframeIndex]
	o.StagnationCount = 0
	fmt.Printf("🔄 SWITCHING TO TIMEFRAME: %s\n", o.CurrentTimeframe)
}

// expandSearchSpace increases parameter ranges when optimization stagnates
func (o *Optimizer) expandSearchSpace() {
	for param, paramRange := range o.Config.ParameterRanges {
		expansion := (paramRange.Max - paramRange.Min) * 0.2
		newRange := Range{
			Min:   math.Max(0.1, paramRange.Min-expansion),
			Max:   paramRange.Max + expansion,
			IsInt: paramRange.IsInt,
		}
		o.Config.ParameterRanges[param] = newRange
	}
	fmt.Println("🔄 Search space expanded!")
}

// tournamentSelection selects a parent using tournament selection
func (o *Optimizer) tournamentSelection(population []*OptimizationResult) *OptimizationResult {
	tournamentSize := 3
	best := population[rand.Intn(len(population))]

	for i := 1; i < tournamentSize; i++ {
		candidate := population[rand.Intn(len(population))]
		if candidate.Score > best.Score {
			best = candidate
		}
	}

	return best
}

// Optimize runs the main optimization loop
func (o *Optimizer) Optimize() error {
	fmt.Printf("🚀 GENERAL PURPOSE CONFIG OPTIMIZER\n")
	fmt.Printf("Symbol: %s | Bot: %s | Timeframe: %s\n", o.Config.Symbol, o.Config.BotName, o.CurrentTimeframe)
	fmt.Printf("Max Generations: %d | Population Size: %d\n", o.Config.MaxGenerations, o.Config.PopulationSize)
	fmt.Printf("Initial Balance: $%.2f | Backtest Count: %d\n", o.Config.InitialBalance, o.Config.BacktestCount)
	fmt.Println(strings.Repeat("=", 80))

	// Initialize population
	population := make([]*OptimizationResult, 0, o.Config.PopulationSize)

	fmt.Println("🧬 INITIALIZING POPULATION")
	for i := 0; i < o.Config.PopulationSize; i++ {
		config := o.generateRandomConfig()

		if err := o.updateConfigFile(config); err != nil {
			return fmt.Errorf("failed to update config file: %v", err)
		}

		result, err := o.runBacktest()
		if err != nil {
			return fmt.Errorf("failed to run backtest: %v", err)
		}

		result.Config = config
		population = append(population, result)
		o.Results = append(o.Results, *result)

		fmt.Printf("Init [%d/%d] Score: %.1f | P&L: $%.2f | Trades: %d | Win%%: %.1f | %s\n",
			i+1, o.Config.PopulationSize, result.Score, result.PnL, result.TradesClosed,
			result.WinRate, map[bool]string{true: "BLOWN", false: "ACTIVE"}[result.IsBlown])

		// Update best result
		if o.BestResult == nil || result.Score > o.BestResult.Score {
			o.BestResult = result
			o.StagnationCount = 0
		}

		time.Sleep(100 * time.Millisecond)
	}

	// Sort population by score
	sort.Slice(population, func(i, j int) bool {
		return population[i].Score > population[j].Score
	})

	// Main optimization loop
	for o.Generation = 1; o.Generation <= o.Config.MaxGenerations; o.Generation++ {
		fmt.Printf("\n🧬 GENERATION %d (%s)\n", o.Generation, o.CurrentTimeframe)
		fmt.Printf("Best Score: %.1f | Stagnation: %d\n", o.BestResult.Score, o.StagnationCount)

		// Create new generation
		newPopulation := make([]*OptimizationResult, 0, o.Config.PopulationSize)

		// Keep elite (top performers)
		eliteCount := int(float64(o.Config.PopulationSize) * o.Config.ElitePercentage)
		if eliteCount < 1 {
			eliteCount = 1
		}

		for i := 0; i < eliteCount; i++ {
			newPopulation = append(newPopulation, population[i])
		}

		// Generate offspring through mutation
		for len(newPopulation) < o.Config.PopulationSize {
			// Tournament selection
			parent := o.tournamentSelection(population)

			// Mutate parent
			childConfig := o.mutateConfig(parent.Config)

			if err := o.updateConfigFile(childConfig); err != nil {
				log.Printf("Failed to update config: %v", err)
				continue
			}

			childResult, err := o.runBacktest()
			if err != nil {
				log.Printf("Failed to run backtest: %v", err)
				continue
			}

			childResult.Config = childConfig
			newPopulation = append(newPopulation, childResult)
			o.Results = append(o.Results, *childResult)

			// Check for improvement
			if childResult.Score > o.BestResult.Score {
				o.BestResult = childResult
				o.StagnationCount = 0
				fmt.Printf("🎯 NEW BEST! Score: %.1f | P&L: $%.2f | Win%%: %.1f\n",
					childResult.Score, childResult.PnL, childResult.WinRate)
			}

			time.Sleep(50 * time.Millisecond)
		}

		// Sort new population
		sort.Slice(newPopulation, func(i, j int) bool {
			return newPopulation[i].Score > newPopulation[j].Score
		})
		population = newPopulation

		// Update stagnation count
		o.StagnationCount++

		// Display generation results
		best := population[0]
		fmt.Printf("Gen Best: Score %.1f | P&L $%.2f | Trades %d | Win%% %.1f\n",
			best.Score, best.PnL, best.TradesClosed, best.WinRate)

		// Handle stagnation
		if o.StagnationCount > o.Config.StagnationLimit {
			if o.StagnationCount > o.Config.StagnationLimit*2 {
				// Switch timeframe if severely stagnant
				o.switchTimeframe()
			} else {
				// Expand search space
				o.expandSearchSpace()
				o.StagnationCount = 0
			}
		}

		// Early termination for excellent results
		if o.BestResult.Score > 200 && o.BestResult.PnL > o.Config.InitialBalance*0.5 {
			fmt.Println("🏆 EXCELLENT RESULT FOUND! Terminating early.")
			break
		}
	}

	// Display final results
	o.displayFinalResults()

	return nil
}

// displayFinalResults shows the optimization results
func (o *Optimizer) displayFinalResults() {
	fmt.Println(strings.Repeat("=", 80))
	fmt.Println("🏆 OPTIMIZATION COMPLETE!")
	fmt.Printf("Best Timeframe: %s\n", o.BestResult.Timeframe)
	fmt.Printf("Best Score: %.1f\n", o.BestResult.Score)
	fmt.Printf("Generations: %d\n", o.Generation)

	if o.BestResult != nil {
		// Update config with best result
		if err := o.updateConfigFile(o.BestResult.Config); err != nil {
			log.Printf("Failed to update final config: %v", err)
		}

		fmt.Printf("\n🎯 FINAL BEST CONFIGURATION:\n")
		fmt.Printf("P&L: $%.2f\n", o.BestResult.PnL)
		fmt.Printf("Win Rate: %.1f%%\n", o.BestResult.WinRate)
		fmt.Printf("Trades: %d/%d\n", o.BestResult.TradesClosed, o.BestResult.TradesPlaced)
		fmt.Printf("Max DD: $%.2f\n", o.BestResult.MaxDrawdown)
		fmt.Printf("Streaks: %d/%d\n", o.BestResult.MaxWins, o.BestResult.MaxLosses)
		fmt.Printf("Account: %s\n", map[bool]string{true: "BLOWN", false: "ACTIVE"}[o.BestResult.IsBlown])

		fmt.Printf("\n📊 OPTIMAL PARAMETERS:\n")
		for param, value := range o.BestResult.Config {
			fmt.Printf("%s: %v\n", param, value)
		}

		// Save results to file
		o.saveResults()
	} else {
		fmt.Println("❌ No viable configuration found!")
	}
}

// saveResults saves optimization results to JSON file
func (o *Optimizer) saveResults() {
	filename := fmt.Sprintf("%s_%s_optimization_results.json", o.Config.Symbol, o.Config.BotName)
	data, err := json.MarshalIndent(o.Results, "", "  ")
	if err != nil {
		log.Printf("Failed to marshal results: %v", err)
		return
	}

	if err := os.WriteFile(filename, data, 0644); err != nil {
		log.Printf("Failed to save results: %v", err)
		return
	}

	fmt.Printf("📁 Results saved to: %s\n", filename)
}

func main() {
	// Command line flags
	var (
		symbol      = flag.String("symbol", "", "Trading symbol (e.g., 'Volatility 75 Index', 'EURUSD', 'BTCUSD')")
		botName     = flag.String("bot", "", "Bot name/folder (e.g., 'gringo', 'alpha')")
		configFile  = flag.String("config", "", "Config file name (e.g., 'env.vol75.json')")
		configPath  = flag.String("optimizer-config", "", "Path to optimizer configuration JSON file")
		generations = flag.Int("generations", 300, "Maximum number of generations")
		population  = flag.Int("population", 20, "Population size")
		balance     = flag.Float64("balance", 50, "Initial balance for backtesting")
		help        = flag.Bool("help", false, "Show help message")
	)

	flag.Parse()

	if *help {
		fmt.Println("🚀 GENERAL PURPOSE CONFIG OPTIMIZER")
		fmt.Println("Usage:")
		fmt.Println("  go run . -symbol=\"Volatility 75 Index\" -bot=gringo -config=env.vol75.json")
		fmt.Println("  go run . -symbol=\"EURUSD\" -bot=alpha -config=env.eurusd.json")
		fmt.Println("  go run . -optimizer-config=optimizer_config.json")
		fmt.Println("")
		fmt.Println("Flags:")
		flag.PrintDefaults()
		fmt.Println("")
		fmt.Println("Examples:")
		fmt.Println("  # Optimize Volatility 75 Index with gringo bot")
		fmt.Println("  go run . -symbol=\"Volatility 75 Index\" -bot=gringo -config=env.vol75.json")
		fmt.Println("")
		fmt.Println("  # Optimize Boom 1000 Index with custom settings")
		fmt.Println("  go run . -symbol=\"Boom 1000 Index\" -bot=gringo -config=env.boom1000.json -generations=400 -population=16")
		fmt.Println("")
		fmt.Println("  # Use custom optimizer configuration file")
		fmt.Println("  go run . -optimizer-config=my_optimizer_config.json")
		return
	}

	// Seed random number generator
	rand.Seed(time.Now().UnixNano())

	var optimizer *Optimizer
	var err error

	if *configPath != "" {
		// Load from configuration file
		optimizer, err = NewOptimizer(*configPath)
		if err != nil {
			log.Fatalf("Failed to create optimizer from config: %v", err)
		}
	} else {
		// Create from command line arguments
		if *symbol == "" || *botName == "" || *configFile == "" {
			fmt.Println("❌ Error: symbol, bot, and config are required when not using -optimizer-config")
			fmt.Println("Use -help for usage information")
			os.Exit(1)
		}

		// Determine bot path
		botPath := filepath.Join("..", *botName)
		if _, err := os.Stat(botPath); os.IsNotExist(err) {
			log.Fatalf("Bot path does not exist: %s", botPath)
		}

		// Create default configuration
		config := createDefaultConfig(*symbol, *botName, botPath, *configFile)

		// Override with command line arguments
		if *generations != 300 {
			config.MaxGenerations = *generations
		}
		if *population != 20 {
			config.PopulationSize = *population
		}
		if *balance != 50 {
			config.InitialBalance = *balance
		}

		optimizer = &Optimizer{
			Config:           *config,
			CurrentTimeframe: config.Timeframes[0],
			TimeframeIndex:   0,
			Results:          make([]OptimizationResult, 0),
		}
	}

	// Validate bot path and config file
	configFullPath := filepath.Join(optimizer.Config.BotPath, optimizer.Config.ConfigFile)
	if _, err := os.Stat(configFullPath); os.IsNotExist(err) {
		log.Fatalf("Config file does not exist: %s", configFullPath)
	}

	// Run optimization
	fmt.Printf("🎯 Starting optimization for %s using %s bot\n", optimizer.Config.Symbol, optimizer.Config.BotName)
	fmt.Printf("📁 Bot Path: %s\n", optimizer.Config.BotPath)
	fmt.Printf("📄 Config File: %s\n", optimizer.Config.ConfigFile)
	fmt.Printf("🔧 Instrument Type: %s\n", detectInstrumentType(optimizer.Config.Symbol))
	fmt.Println("")

	if err := optimizer.Optimize(); err != nil {
		log.Fatalf("Optimization failed: %v", err)
	}
}
