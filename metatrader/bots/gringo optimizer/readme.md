# 🚀 General Purpose Config Optimizer

A highly efficient Go-based optimizer that can optimize any trading bot configuration through genetic algorithm optimization with automatic timeframe switching and parameter adaptation.

## Features

- **Multi-Bot Support**: Works with any bot (gringo, alpha, etc.)
- **Instrument Detection**: Automatically detects instrument type (forex, crypto, synthetic, gold)
- **Adaptive Timeframes**: Switches timeframes if results are poor
- **Genetic Algorithm**: Uses tournament selection, elitism, and mutation
- **Parameter Constraints**: Ensures logical parameter relationships
- **Real-time Progress**: Live optimization progress with best result tracking
- **Result Persistence**: Saves detailed results to JSON files
- **Disk Cleanup**: Automatically removes temporary tick JSON files

## Usage

### Basic Usage

```bash
# Optimize Volatility 75 Index with gringo bot
go run . -symbol="Volatility 75 Index" -bot=gringo -config=env.vol75.json

# Optimize EURUSD with alpha bot
go run . -symbol="EURUSD" -bot=alpha -config=env.eurusd.json

# Optimize Boom 1000 Index with custom settings
go run . -symbol="Boom 1000 Index" -bot=gringo -config=env.boom1000.json -generations=400 -population=16
```

### Advanced Usage with Config File

```bash
# Use custom optimizer configuration
go run . -optimizer-config=optimizer_config_example.json
```

### Command Line Options

- `-symbol`: Trading symbol (required)
- `-bot`: Bot name/folder (required)
- `-config`: Config file name (required)
- `-generations`: Maximum generations (default: 300)
- `-population`: Population size (default: 20)
- `-balance`: Initial balance (default: 50)
- `-optimizer-config`: Path to optimizer config JSON
- `-help`: Show help message

## Instrument Types & Auto-Detection

The optimizer automatically detects instrument types and applies appropriate parameter ranges:

### Synthetic Indices
- **Symbols**: Volatility 75, Boom 1000, Crash 1000, etc.
- **Characteristics**: High volatility, wide spreads, large pip movements
- **Optimized for**: Spike capture, high-frequency trading

### Forex Pairs
- **Symbols**: EURUSD, GBPUSD, USDJPY, etc.
- **Characteristics**: Standard spreads, moderate volatility
- **Optimized for**: Traditional scalping strategies

### Cryptocurrencies
- **Symbols**: BTCUSD, ETHUSD, ADAUSD, etc.
- **Characteristics**: High volatility, 24/7 trading
- **Optimized for**: Momentum and trend strategies

### Gold/Metals
- **Symbols**: XAUUSD, Gold, etc.
- **Characteristics**: Moderate volatility, safe haven asset
- **Optimized for**: Range and breakout strategies

## Optimization Process

1. **Population Initialization**: Creates random configurations within parameter ranges
2. **Fitness Evaluation**: Runs backtests and calculates multi-factor scores
3. **Selection**: Uses tournament selection to choose parents
4. **Mutation**: Creates offspring with parameter mutations
5. **Elitism**: Preserves top performers across generations
6. **Adaptation**: Expands search space or switches timeframes when stagnant

## Scoring System

The optimizer uses a multi-factor scoring system:

- **P&L (40%)**: Primary profitability metric
- **Win Rate (25%)**: Trading consistency
- **Activity (15%)**: Number of trades executed
- **Risk Control (15%)**: Drawdown management
- **Consistency (5%)**: Win/loss streak quality

## Configuration File Format

```json
{
  "symbol": "Volatility 75 Index",
  "bot_name": "gringo",
  "bot_path": "../gringo",
  "config_file": "env.vol75.json",
  "timeframes": ["M1", "M5", "M15"],
  "max_generations": 300,
  "population_size": 16,
  "initial_balance": 50,
  "backtest_count": 5760,
  "mutation_rate": 0.4,
  "elite_percentage": 0.25,
  "stagnation_limit": 20,
  "parameter_ranges": {
    "fast_ema_period": {"min": 2, "max": 25, "is_int": true},
    "slow_ema_period": {"min": 8, "max": 60, "is_int": true},
    // ... more parameters
  }
}
```

## Output Files

- **`{symbol}_{bot}_optimization_results.json`**: Detailed results for all tests
- **Updated config file**: Best configuration written back to bot's config file

## Performance Tips

1. **Population Size**: 
   - Forex: 20-25
   - Synthetic: 15-20
   - Crypto: 15-20

2. **Generations**:
   - Quick optimization: 100-200
   - Thorough optimization: 300-500

3. **Timeframes**:
   - Start with M1 for high-frequency strategies
   - M5/M15 for swing strategies

## Examples

### Volatility 75 Index Optimization
```bash
go run . -symbol="Volatility 75 Index" -bot=gringo -config=env.vol75.json -generations=350 -population=16
```

### EURUSD Forex Optimization
```bash
go run . -symbol="EURUSD" -bot=alpha -config=env.eurusd.json -generations=300 -population=20
```

### Bitcoin Crypto Optimization
```bash
go run . -symbol="BTCUSD" -bot=gringo -config=env.btcusd.json -generations=250 -population=18
```

## Troubleshooting

1. **Bot path not found**: Ensure bot folder exists relative to optimizer
2. **Config file not found**: Check config file path and name
3. **No trades generated**: Adjust parameter ranges or timeframes
4. **Poor results**: Try different timeframes or expand parameter ranges

## Integration with Existing Bots

The optimizer works with any bot that:
1. Accepts standard command line arguments
2. Uses JSON configuration files
3. Outputs standard backtest metrics
4. Supports simulation mode

No bot modifications required!
