package main

import (
	"strings"
)

// Default parameter ranges for different instrument types
var DefaultParameterRanges = map[string]map[string]Range{
	"forex": {
		"fast_ema_period":        {Min: 2, Max: 20, IsInt: true},
		"slow_ema_period":        {Min: 8, Max: 50, IsInt: true},
		"rsi_period":             {Min: 3, Max: 25, IsInt: true},
		"rsi_oversold":           {Min: 15, Max: 35, IsInt: true},
		"rsi_overbought":         {Min: 65, Max: 85, IsInt: true},
		"bb_period":              {Min: 10, Max: 30, IsInt: true},
		"bb_deviation":           {Min: 1.5, Max: 3.0, IsInt: false},
		"min_pip_move":           {Min: 0.0001, Max: 0.01, IsInt: false},
		"quick_profit_pips":      {Min: 5, Max: 50, IsInt: true},
		"stop_loss_pips":         {Min: 10, Max: 100, IsInt: true},
		"risk_per_trade_pct":     {Min: 0.5, Max: 3.0, IsInt: false},
		"fixed_volume":           {Min: 0.01, Max: 1.0, IsInt: false},
		"max_concurrent_trades":  {Min: 1, Max: 10, IsInt: true},
		"max_daily_trades":       {Min: 10, Max: 1000, IsInt: true},
		"min_trade_interval_ms":  {Min: 500, Max: 5000, IsInt: true},
	},
	"synthetic": {
		"fast_ema_period":        {Min: 2, Max: 25, IsInt: true},
		"slow_ema_period":        {Min: 8, Max: 60, IsInt: true},
		"rsi_period":             {Min: 3, Max: 30, IsInt: true},
		"rsi_oversold":           {Min: 5, Max: 40, IsInt: true},
		"rsi_overbought":         {Min: 60, Max: 95, IsInt: true},
		"bb_period":              {Min: 5, Max: 35, IsInt: true},
		"bb_deviation":           {Min: 1.0, Max: 5.0, IsInt: false},
		"min_pip_move":           {Min: 0.5, Max: 15.0, IsInt: false},
		"quick_profit_pips":      {Min: 20, Max: 800, IsInt: true},
		"stop_loss_pips":         {Min: 50, Max: 1200, IsInt: true},
		"risk_per_trade_pct":     {Min: 0.3, Max: 8.0, IsInt: false},
		"fixed_volume":           {Min: 0.05, Max: 3.0, IsInt: false},
		"max_concurrent_trades":  {Min: 1, Max: 15, IsInt: true},
		"max_daily_trades":       {Min: 50, Max: 100000, IsInt: true},
		"min_trade_interval_ms":  {Min: 50, Max: 8000, IsInt: true},
	},
	"crypto": {
		"fast_ema_period":        {Min: 3, Max: 15, IsInt: true},
		"slow_ema_period":        {Min: 10, Max: 40, IsInt: true},
		"rsi_period":             {Min: 5, Max: 20, IsInt: true},
		"rsi_oversold":           {Min: 20, Max: 35, IsInt: true},
		"rsi_overbought":         {Min: 65, Max: 80, IsInt: true},
		"bb_period":              {Min: 15, Max: 25, IsInt: true},
		"bb_deviation":           {Min: 2.0, Max: 3.0, IsInt: false},
		"min_pip_move":           {Min: 1.0, Max: 10.0, IsInt: false},
		"quick_profit_pips":      {Min: 10, Max: 200, IsInt: true},
		"stop_loss_pips":         {Min: 20, Max: 400, IsInt: true},
		"risk_per_trade_pct":     {Min: 0.8, Max: 2.5, IsInt: false},
		"fixed_volume":           {Min: 0.01, Max: 0.5, IsInt: false},
		"max_concurrent_trades":  {Min: 1, Max: 5, IsInt: true},
		"max_daily_trades":       {Min: 20, Max: 500, IsInt: true},
		"min_trade_interval_ms":  {Min: 300, Max: 3000, IsInt: true},
	},
	"gold": {
		"fast_ema_period":        {Min: 5, Max: 20, IsInt: true},
		"slow_ema_period":        {Min: 15, Max: 50, IsInt: true},
		"rsi_period":             {Min: 8, Max: 25, IsInt: true},
		"rsi_oversold":           {Min: 25, Max: 35, IsInt: true},
		"rsi_overbought":         {Min: 65, Max: 75, IsInt: true},
		"bb_period":              {Min: 15, Max: 30, IsInt: true},
		"bb_deviation":           {Min: 1.8, Max: 2.5, IsInt: false},
		"min_pip_move":           {Min: 0.1, Max: 2.0, IsInt: false},
		"quick_profit_pips":      {Min: 10, Max: 100, IsInt: true},
		"stop_loss_pips":         {Min: 20, Max: 200, IsInt: true},
		"risk_per_trade_pct":     {Min: 1.0, Max: 3.0, IsInt: false},
		"fixed_volume":           {Min: 0.01, Max: 0.2, IsInt: false},
		"max_concurrent_trades":  {Min: 1, Max: 8, IsInt: true},
		"max_daily_trades":       {Min: 10, Max: 200, IsInt: true},
		"min_trade_interval_ms":  {Min: 1000, Max: 10000, IsInt: true},
	},
}

// detectInstrumentType determines the instrument type from symbol
func detectInstrumentType(symbol string) string {
	symbol = strings.ToUpper(symbol)
	
	if strings.Contains(symbol, "VOLATILITY") || strings.Contains(symbol, "BOOM") || 
	   strings.Contains(symbol, "CRASH") || strings.Contains(symbol, "STEP") || 
	   strings.Contains(symbol, "JUMP") || strings.Contains(symbol, "VOL") {
		return "synthetic"
	}
	
	if strings.Contains(symbol, "BTC") || strings.Contains(symbol, "ETH") || 
	   strings.Contains(symbol, "ADA") || strings.Contains(symbol, "SOL") {
		return "crypto"
	}
	
	if strings.Contains(symbol, "XAU") || strings.Contains(symbol, "GOLD") {
		return "gold"
	}
	
	return "forex"
}

// createDefaultConfig creates a default optimizer configuration
func createDefaultConfig(symbol, botName, botPath, configFile string) *OptimizerConfig {
	instrumentType := detectInstrumentType(symbol)
	
	config := &OptimizerConfig{
		Symbol:           symbol,
		BotName:          botName,
		BotPath:          botPath,
		ConfigFile:       configFile,
		Timeframes:       []string{"M1", "M5", "M15", "M30"},
		MaxGenerations:   300,
		PopulationSize:   20,
		InitialBalance:   50,
		BacktestCount:    5760,
		ParameterRanges:  DefaultParameterRanges[instrumentType],
		MutationRate:     0.3,
		ElitePercentage:  0.2,
		StagnationLimit:  25,
	}
	
	// Adjust for synthetic indices
	if instrumentType == "synthetic" {
		config.PopulationSize = 16
		config.MaxGenerations = 350
		config.MutationRate = 0.4
		config.StagnationLimit = 20
	}
	
	return config
}
