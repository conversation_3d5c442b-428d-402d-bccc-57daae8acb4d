package main

import (
	. "bridge"
	"encoding/json"
	"fmt"
	. "shared"
	"slices"
	"strings"
	"sync"
	"time"

	"github.com/IBM/sarama"
)

var config Map = Map{}

type Config struct {
	Symbol         string
	Remote_ticking bool
	Kafka_uri      string
	// Sizing and transforms
	Scaling struct {
		// mirror | proportional_equity | proportional_balance | fixed | equity_risk
		Mode            string  // Sizing mode
		Proportional_by string  // "equity" or "balance"
		Fixed_lot       float32 // Used when Mode=fixed
		Risk_percent    float32 // Used when Mode=equity_risk (percent of equity per trade)
		Reverse         bool    // Reverse BUY<->SELL on copy
	}
	// e.g. ["XAU -> GOLD", "XBT -> BTC"]
	Currency_maps []string
	// FR-6 Risk Controls
	Risk struct {
		Max_aggregate_lots     float32
		Max_concurrent_trades  int
		Max_daily_drawdown_pct float32
		Equity_stop_abs        float32
		Equity_stop_pct        float32
		Symbol_whitelist       []string
		Symbol_blacklist       []string
		// HH:MM-HH:MM in local time, e.g. ["08:00-16:30","19:00-22:00"]
		Time_windows []string
	}
}

// tickLogger is a simple per-tick in-memory logger with a max number of lines.
type tickLogger struct {
	lines []string
	max   int
}

func newTickLogger(max int) *tickLogger {
	if max <= 0 {
		max = 500
	}
	return &tickLogger{lines: make([]string, 0, max), max: max}
}

func (l *tickLogger) Log(args ...interface{}) {
	if l == nil {
		return
	}
	line := fmt.Sprint(args...)
	l.lines = append(l.lines, line)
	if len(l.lines) > l.max {
		start := len(l.lines) - l.max
		copy(l.lines, l.lines[start:])
		l.lines = l.lines[:l.max]
	}
}

// runtime risk state
var dayStart = time.Now().Format("2006-01-02")
var startEquity float32 = 0
var maxEquityToday float32 = 0

func applyCurrencyMaps(symbol string, maps []string) string {
	out := symbol
	for _, m := range maps {
		parts := strings.Split(m, "->")
		if len(parts) != 2 {
			continue
		}
		from := strings.TrimSpace(parts[0])
		to := strings.TrimSpace(parts[1])
		if from == "" || to == "" {
			continue
		}
		out = strings.ReplaceAll(out, from, to)
	}
	return out
}

func reverseType(t int64, reverse bool) int64 {
	if !reverse {
		return t
	}
	// Assume 0=BUY, 1=SELL; flip when applicable
	if t == 0 {
		return 1
	}
	if t == 1 {
		return 0
	}
	return t
}

func inWhitelist(symbol string, list []string) bool {
	if len(list) == 0 {
		return true
	}
	for _, s := range list {
		if strings.EqualFold(strings.TrimSpace(s), symbol) {
			return true
		}
	}
	return false
}

func inBlacklist(symbol string, list []string) bool {
	for _, s := range list {
		if strings.EqualFold(strings.TrimSpace(s), symbol) {
			return true
		}
	}
	return false
}

func atoiSafe(s string) int {
	n := 0
	for _, ch := range s {
		if ch >= '0' && ch <= '9' {
			n = n*10 + int(ch-'0')
		}
	}
	return n
}

func nowWithinWindows(wins []string, now time.Time) bool {
	if len(wins) == 0 {
		return true
	}
	for _, w := range wins {
		parts := strings.Split(w, "-")
		if len(parts) != 2 {
			continue
		}
		start := strings.TrimSpace(parts[0])
		end := strings.TrimSpace(parts[1])
		if len(start) != 5 || len(end) != 5 {
			continue
		}
		sH, sM := atoiSafe(start[:2]), atoiSafe(start[3:])
		eH, eM := atoiSafe(end[:2]), atoiSafe(end[3:])
		startT := time.Date(now.Year(), now.Month(), now.Day(), sH, sM, 0, 0, now.Location())
		endT := time.Date(now.Year(), now.Month(), now.Day(), eH, eM, 0, 0, now.Location())
		if !endT.After(startT) {
			// crosses midnight
			endT = endT.Add(24 * time.Hour)
			if now.Before(startT) {
				now = now.Add(24 * time.Hour)
			}
		}
		if now.Equal(startT) || (now.After(startT) && now.Before(endT)) {
			return true
		}
	}
	return false
}

func shouldBlockNewTrade(o Options, cfg Config, symbol string) bool {
	// symbol filters
	if !inWhitelist(symbol, cfg.Risk.Symbol_whitelist) {
		return true
	}
	if inBlacklist(symbol, cfg.Risk.Symbol_blacklist) {
		return true
	}
	// time windows
	if !nowWithinWindows(cfg.Risk.Time_windows, time.Now()) {
		return true
	}
	// account snapshot
	acct, err := o.Client.GetAccount(o.Ctx, &EmptyType{})
	if err == nil && acct != nil {
		// day roll
		today := time.Now().Format("2006-01-02")
		if today != dayStart {
			dayStart = today
			maxEquityToday = acct.Equity
			if startEquity == 0 {
				startEquity = acct.Equity
			}
		}
		if acct.Equity > maxEquityToday {
			maxEquityToday = acct.Equity
		}
		// equity stop absolute
		if cfg.Risk.Equity_stop_abs > 0 && acct.Equity <= cfg.Risk.Equity_stop_abs {
			return true
		}
		// equity stop percent (from startEquity)
		if cfg.Risk.Equity_stop_pct > 0 && startEquity > 0 {
			if acct.Equity <= startEquity*(1.0-float32(cfg.Risk.Equity_stop_pct)/100.0) {
				return true
			}
		}
		// daily drawdown
		if cfg.Risk.Max_daily_drawdown_pct > 0 && maxEquityToday > 0 {
			dd := (maxEquityToday - acct.Equity) / maxEquityToday * 100.0
			if dd >= cfg.Risk.Max_daily_drawdown_pct {
				return true
			}
		}
	}
	// positions constraints
	trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
	if err == nil && trades != nil {
		if cfg.Risk.Max_concurrent_trades > 0 {
			if len(trades.Positions) >= cfg.Risk.Max_concurrent_trades {
				return true
			}
		}
		if cfg.Risk.Max_aggregate_lots > 0 {
			var sum float32 = 0
			for _, p := range trades.Positions {
				sum += p.Volume
			}
			if sum >= cfg.Risk.Max_aggregate_lots {
				return true
			}
		}
	}
	return false
}

func computeVolume(o Options, cfg Config, masterVol float32, masterEquity float32, _ *AccountPositionType) float32 {
	// slave account snapshot
	acct, _ := o.Client.GetAccount(o.Ctx, &EmptyType{})
	slaveEquity := float32(0)
	if acct != nil {
		slaveEquity = acct.Equity
		if startEquity == 0 {
			startEquity = acct.Equity
			maxEquityToday = acct.Equity
		}
	}
	mode := strings.ToLower(strings.TrimSpace(cfg.Scaling.Mode))
	switch mode {
	case "proportional_equity", "equity":
		if masterEquity > 0 && slaveEquity > 0 {
			return masterVol * (slaveEquity / masterEquity)
		}
	case "proportional_balance", "balance":
		// Balance not provided by master; fall back to equity ratio
		if masterEquity > 0 && slaveEquity > 0 {
			return masterVol * (slaveEquity / masterEquity)
		}
	case "fixed":
		if cfg.Scaling.Fixed_lot > 0 {
			return cfg.Scaling.Fixed_lot
		}
	case "equity_risk", "risk":
		// NOTE: Requires symbol specs and stop distance; not implemented here.
		// Falls back to 1:1 if sizing cannot be computed.
		return masterVol
	case "mirror", "":
		fallthrough
	default:
		// 1:1
	}
	return masterVol
}

func main() {
	is_ticking := false

	// per-tick logger and shared async buffer for goroutine logs
	tl := newTickLogger(500)
	var asyncMu sync.Mutex
	var asyncLines []string
	appendAsync := func(lines ...string) {
		asyncMu.Lock()
		asyncLines = append(asyncLines, lines...)
		if len(asyncLines) > 1000 {
			start := len(asyncLines) - 1000
			copy(asyncLines, asyncLines[start:])
			asyncLines = asyncLines[:1000]
		}
		asyncMu.Unlock()
	}

	var fn Callback = func(o Options, tick *TickType) []string {

		c := Config{}
		MapToStruct(config, &c)

		if is_ticking == false {
			is_ticking = true
			go func() {

				consumer, err := sarama.NewConsumer([]string{c.Kafka_uri}, nil)
				if err != nil {
					appendAsync(fmt.Sprint("Failed to start Sarama consumer:", err))
				}
				defer consumer.Close()
				partitionConsumer, err := consumer.ConsumePartition(o.Id, 0, sarama.OffsetNewest)
				if err != nil {
					appendAsync(fmt.Sprint("Failed to start partition consumer:", err))
				}
				defer partitionConsumer.Close()
				for {
					select {
					case msg := <-partitionConsumer.Messages():
						appendAsync(fmt.Sprintf("Consumed message: %s at offset %d", string(msg.Value), msg.Offset))

						message := TradeCopyMessage{}

						if err := json.Unmarshal(msg.Value, &message); err != nil {
							appendAsync(fmt.Sprint("json unmarshal error:", err))
							continue
						}

						position := message.Position

						switch message.Message_type {
						case NEW:
							// sizing, symbol mapping, reverse copy, and risk checks
							mappedSymbol := applyCurrencyMaps(position.Symbol, c.Currency_maps)
							if shouldBlockNewTrade(o, c, mappedSymbol) {
								appendAsync(fmt.Sprint("Risk control blocked NEW trade for ", mappedSymbol))
								continue
							}
							vol := computeVolume(o, c, position.Volume, message.MasterEquity, position)
							t := reverseType(position.Type, c.Scaling.Reverse)
							o.Client.PlaceTrade(o.Ctx, &PlaceTradeRequest{
								Symbol:     mappedSymbol,
								Magic:      &position.Magic,
								ActionType: t,
								StopLoss:   &position.StopLoss,
								TakeProfit: &position.TakeProfit,
								Volume:     vol,
							})
						case MODIFY:
							trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
							if err != nil {
								appendAsync(fmt.Sprint("Failed to get current positions:", err))
								return
							}
							position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool { return c.Magic == position.Magic })
							if position_index == -1 {
								appendAsync("Trade not found")
								return
							} else {
								client_position := trades.Positions[position_index]
								o.Client.ModifyTrade(o.Ctx, &ModifyTradeRequest{
									Ticket:     client_position.Ticket,
									StopLoss:   position.StopLoss,
									TakeProfit: position.TakeProfit,
								})
							}
						case CLOSE:
							trades, err := o.Client.GetPositions(o.Ctx, &EmptyType{})
							if err != nil {
								appendAsync(fmt.Sprint("Failed to get current positions:", err))
								return
							}
							position_index := slices.IndexFunc(trades.Positions, func(c *AccountPositionType) bool { return c.Magic == position.Magic })
							if position_index == -1 {
								appendAsync("Trade not found")
								return
							} else {
								client_position := trades.Positions[position_index]
								o.Client.CloseTrade(o.Ctx, &CloseTradeRequest{
									Ticket: client_position.Ticket,
									Volume: &client_position.Volume,
								})
							}
						}

					case err := <-partitionConsumer.Errors():
						appendAsync(fmt.Sprint("Error: ", err.Error()))
					}
				}
			}()
		}

		// Drain async logs into this tick's logger and return
		asyncMu.Lock()
		if len(asyncLines) > 0 {
			tl.lines = append(tl.lines, asyncLines...)
			asyncLines = asyncLines[:0]
		}
		asyncMu.Unlock()

		out := append([]string(nil), tl.lines...)
		tl.lines = tl.lines[:0]
		return out
	}

	Initialize(&config, fn)

}
