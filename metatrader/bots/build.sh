#!/bin/bash

args=("$@")
echo "args: ${args[@]}"
PROJECT="${args[0]}"

if [ -z "$PROJECT" ]; then
  echo "Error: PROJECT is not set. Please provide the project name."
  exit 1
fi

echo "Building $PROJECT for local..."
cd "$PROJECT"

go build -o $PROJECT
cp -r ./$PROJECT ../../../node-manager/bots/local/$PROJECT
rm -f ./$PROJECT

echo "Building $PROJECT for linux..."
GOOS=linux GOARCH=amd64 go build -o $PROJECT

cp -r ./$PROJECT ../../../node-manager/bots/$PROJECT
rm -f ./$PROJECT

echo "Build complete."