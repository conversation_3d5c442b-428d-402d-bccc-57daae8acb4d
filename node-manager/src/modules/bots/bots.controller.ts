import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards } from '@nestjs/common';
import { BotsService } from './bots.service';
import { AdminHttpGuard } from 'src/guards/auth.guard';
import { InitBotDto, ToggleBotStateDto } from './utils/dto.utils';

@Controller('bots')
export class BotsController {
  constructor(private botsService: BotsService) { }

  @UseGuards(AdminHttpGuard)
  @Post('init')
  async initNewBot(@Body() body: InitBotDto) {
    const data = await this.botsService.initNewBot(body);
    return {
      data,
      message: 'Initiated bot',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post('toggle')
  async toggleState(@Body() body: ToggleBotStateDto) {
    const data = await this.botsService.toggleBotState(body);
    return {
      data,
      message: 'Toggled state of bot',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Get(':id')
  async getStatus(@Param('id') id: string) {
    const data = await this.botsService.getStatus(id);
    return {
      data,
      message: 'Got status of bot',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Get(':id/logs')
  async getLogs(@Param('id') id: string) {
    const data = await this.botsService.getLogs(id);
    return {
      data,
      message: 'Got logs of bot',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Post(':id/logs/clear')
  async clearLogs(@Param('id') id: string) {
    const data = await this.botsService.clearLogs(id);
    return {
      data,
    }
  }
  
  @UseGuards(AdminHttpGuard)
  @Delete(':id')
  async stopBot(@Param('id') id: string) {
    const data = await this.botsService.stopBot(id);
    return {
      data,
      message: 'Stopped bot',
    };
  }

  @UseGuards(AdminHttpGuard)
  @Put(':id')
  async updateBot(@Param('id') id: string, @Body() body: Object) {
    const data = await this.botsService.updateBot(id, body);
    return {
      data,
      message: 'Updated bot',
    };
  }


}
