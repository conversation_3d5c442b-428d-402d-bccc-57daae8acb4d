import {
  BadRequestException,
  Injectable,
  Logger,
  PreconditionFailedException,
} from '@nestjs/common';
import * as fs from 'fs';
import { isProduction } from 'src/utils/constants';
import { exec } from 'src/utils/functions';
import { WebSocket, RawData } from 'ws';
import { InitBotDto, ToggleBotStateDto } from './utils/dto.utils';

export const MemCache = {
  totalRunning: 0,
  bots: {} as {
    [id: string]: {
      id: string;
      controller?: AbortController;
      client?: WebSocket;
      name: string;
    };
  },
};

@Injectable()
export class BotsService {
  private botsDir: string;
  constructor(private logger: Logger) {
    this.botsDir = isProduction
      ? '/config/node-manager/dist/bots'
      : 'bots/local';
  }
  async initNewBot(data: InitBotDto) {
    if (data.is_internal === true) {
      const name = data.name;
      if (name === undefined) throw new BadRequestException('Name is required');

      const botPath = `${this.botsDir}/${name}`;

      try {
        // Check if file exists using exec
        await exec('sudo test', '.', ['-f', botPath]);
      } catch (error) {
        throw new BadRequestException('Bot does not exist');
      }

      // Create execution directory using exec
      const botExecDirectory = `${this.botsDir}/exec/${name}-${data.id}`;
      await exec('sudo mkdir', '.', ['-p', botExecDirectory]);

      // Copy bot to execution directory using exec
      const botExecPath = `${botExecDirectory}/bin`;
      await exec('sudo cp', '.', [botPath, botExecPath]);
      
      // Set execute permissions on the bot binary
      await exec('sudo chmod', '.', ['+x', botExecPath]);

      // Create json file in bot exec directory using a temporary file
      const jsonFilePath = `${botExecDirectory}/env.json`;
      const tempJsonPath = `/tmp/env-${data.id}.json`;
      
      // Write to temp file first (no sudo needed)
      fs.writeFileSync(tempJsonPath, JSON.stringify(data.config));
      
      // Move temp file to final destination with sudo
      await exec('sudo mv', '.', [tempJsonPath, jsonFilePath]);

      const controller = new AbortController();
      MemCache.bots[data.id] = { id: data.id, controller, name };
      const { signal } = controller;

      // start bot
      const args = `-auth_token ${data.auth_token} -id ${data.id} -rpc_uri ${data.rpc_server} -ws_uri ${data.ws_server}`;

      exec(`./bin`, botExecDirectory, [...args.split(' ')], signal, true).catch(
        (e) => {
          this.logger.error(e);
          delete MemCache.bots[data.id];
        },
      );
    } else throw new BadRequestException('Not supported');
  }

  async getStatus(id: string) {
    const client = MemCache.bots[id]?.client;
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({ cmd: 'GET_STATUS' }));
      return new Promise((res, rej) => {
        const callback = (data: RawData) => {
          client.removeListener('message', callback);
          const message = data.toString();
          res(JSON.parse(message));
        };
        client.on('message', callback);
        client.on('error', rej);

        //timeout
        setTimeout(() => {
          client.removeListener('message', callback);
          rej(new Error('Timeout'));
        }, 5000);
      });
    }

    throw new PreconditionFailedException('Bot is not running');
  }

  async getLogs(id: string) {
    const client = MemCache.bots[id]?.client;
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({ cmd: 'GET_LOGS' }));
      return new Promise((res, rej) => {
        const callback = (data: RawData) => {
          client.removeListener('message', callback);
          const message = data.toString();
          res(JSON.parse(message));
        };
        client.on('message', callback);
        client.on('error', rej);

        //timeout
        setTimeout(() => {
          client.removeListener('message', callback);
          rej(new Error('Timeout'));
        }, 5000);
      });
    }

    throw new PreconditionFailedException('Bot is not running');
  }

  async clearLogs(id: string) {
    const client = MemCache.bots[id]?.client;
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({ cmd: 'CLEAR_LOGS' }));
      return
    }

    throw new PreconditionFailedException('Bot is not running');
  }

  async stopBot(id: string) {
    const controller = MemCache.bots[id]?.controller;
    if (controller) {
      controller.abort();

      const {name} =  MemCache.bots[id];
      const botExecDirectory = `${this.botsDir}/exec/${name}-${id}`;
      //remove dir 
      await exec('sudo rm', '.', ['-rf', botExecDirectory]);
      MemCache.totalRunning--;
      delete MemCache.bots[id];
      return
    }
  }

  async updateBot(id: string, body: Object) {
    const client = MemCache.bots[id]?.client;
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({ cmd: 'UPDATE', ...body }));
      return
    }
    throw new PreconditionFailedException('Bot is not running');
  }

  async toggleBotState(data: ToggleBotStateDto) {
    const client = MemCache.bots[data.id]?.client;
    if (client && client.readyState === WebSocket.OPEN) {
      client.send(JSON.stringify({ cmd: data.cmd }));
      return
    }
    throw new PreconditionFailedException('Bot is not running');
  }
}
